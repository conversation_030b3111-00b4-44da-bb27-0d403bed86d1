
interface IProps {
    profileImg: string;
    name?: string;
    styles?: string;
}
const Avatar = ({ profileImg, name, styles }: IProps) => {
    return (
        <>
            <div className={`rounded-full ${styles} flex items-center justify-center`}>
                {profileImg ?
                    <img src={profileImg} className="w-full object-fill" />
                    :
                    <span className="text-lg font-bold">{name?.at(0)}</span>
                }
            </div>
        </>
    )
}
export default Avatar