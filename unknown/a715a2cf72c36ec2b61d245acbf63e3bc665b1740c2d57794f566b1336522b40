'use client'
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { RootState } from "@/store"
import { handleCoachSportInputChange, updateTeamData } from "@/store/slices/coach/coachSportSlice"
import { useDispatch, useSelector } from "react-redux"

const TeamsCoaching = () => {
    const { toggleTeamCoaching, currentTeamList } = useSelector((state: RootState) => state.coachSport)
    const dispatch = useDispatch()

    const handleOnChange = (name: string, value: boolean) => {
        dispatch(handleCoachSportInputChange({ name, value }))
    }

    const handleUpdate = (id: number, field: "team" | "isPrimary", value: string | boolean) => {
        dispatch(updateTeamData({ id, field, value }))
    }

    return (
        <>
            <div className="bg-slate-100 flex flex-col gap-8 rounded-lg p-5">
                <div className="flex items-center justify-center gap-5">
                    <h3 className="tex-xl font-bold">Teams Current Coaching</h3>
                    <Switch
                        checked={toggleTeamCoaching}
                        onCheckedChange={(checked) => handleOnChange('toggleTeamCoaching', checked)} />
                </div>

                {toggleTeamCoaching ? <>
                    {true ?
                        <div className="flex flex-col gap-5">
                            {currentTeamList?.map(team => (
                                <div className="grid grid-cols-1 md:grid-cols-3 items-center gap-5" key={team?.id + 'ct'}>
                                    <Input
                                        value={team?.team}
                                        className="md:col-span-2"
                                        placeholder="Enter Team"
                                        onChange={(e) => handleUpdate(team?.id, "team", e.target.value)}
                                    />
                                    <div className="flex items-center gap-2">
                                        <Label>Primary Team?</Label>
                                        <Switch checked={team?.isPrimary}
                                            onCheckedChange={(checked) => handleUpdate(team?.id, "isPrimary", checked)}
                                        />
                                    </div>
                                </div>
                            ))}
                        </div>
                        :
                        <>
                            {/* View only */}
                        </>}
                </> : null}
            </div >
        </>
    )
}
export default TeamsCoaching