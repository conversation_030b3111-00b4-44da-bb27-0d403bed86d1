import ImageUpload from "@/components/common/ImagesUpload"
import { But<PERSON> } from "@/components/ui/button"
import { RootState } from "@/store"
import { handleCoachSportInputChange } from "@/store/slices/coach/coachSportSlice"
import { useDispatch, useSelector } from "react-redux"

const ImageGallery = () => {
    const { imagesGalleryList } = useSelector((state: RootState) => state.coachSport)
    const dispatch = useDispatch()

    const handleOnChange = (files) => {
        dispatch(handleCoachSportInputChange({ name: 'imagesGalleryList', value: files }))

    }

    return (
        <>
            <div className="flex flex-col items-center gap-5 p-5 rounded-lg bg-slate-100">
                <h3 className="font-bold text-xl">Image Gallery</h3>

                <ImageUpload
                    value={imagesGalleryList}
                    onChange={handleOnChange}
                    maxImages={4}
                    maxSize={1024}
                    width="w-76"
                    name='imagesGalleryList'
                    imageNameRequired={true}
                />

                <div className="flex justify-end self-end">
                    <Button>Save</Button>
                </div>

                <div className="flex flex-col gap-5">

                    {/* <ImagesUpload
                        name='sprAdmOrgBanner'
                        value={sprAdmOrgBanner}
                        onChange={handleImageChange('sprAdmOrgBanner')}
                        maxImages={8}
                    /> */}

                </div>


            </div>
        </>
    )
}
export default ImageGallery