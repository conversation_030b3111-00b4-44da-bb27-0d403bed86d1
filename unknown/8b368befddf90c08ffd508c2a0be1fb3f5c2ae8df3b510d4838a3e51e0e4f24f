import { Button } from "@/components/ui/button";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Clock } from "lucide-react";
import { useEffect, useState } from "react";

interface TimeValue {
    hours: number;
    minutes: number;
    isPM: boolean;
}

interface DateTimePickerProps {
    onChange: (name: string, value: Date | TimeValue) => void;
    date?: Date;
    name: string;
    incrementMints?: number;
}

const TimePicker: React.FC<DateTimePickerProps> = ({
    onChange,
    date,
    name,
    incrementMints,
}) => {
    const [hours, setHours] = useState<number>(12);
    const [minutes, setMinutes] = useState<number>(0);
    const [isPM, setIsPM] = useState<boolean>(false);

    // Initialize based on 'date' if provided
    useEffect(() => {
        if (date) {
            const hrs = date.getHours();
            setIsPM(hrs >= 12);
            const displayHours = hrs % 12 === 0 ? 12 : hrs % 12;
            setHours(displayHours);
            setMinutes(date.getMinutes());
        } else {
            setHours(name === "toTime" ? 11 : 12);
            setMinutes(name === "toTime" ? 59 : 0);
            setIsPM(name === "toTime");
        }
    }, [date, name]);

    // Trigger change
    useEffect(() => {
        if (date) {
            const updatedDate = new Date(date);
            let adjustedHours = hours;
            if (isPM && hours !== 12) {
                adjustedHours += 12;
            } else if (!isPM && hours === 12) {
                adjustedHours = 0;
            }
            updatedDate.setHours(adjustedHours, minutes);
            onChange(name, updatedDate);
        } else {
            onChange(name, { hours, minutes, isPM });
        }
    }, [hours, minutes, isPM]);

    const incrementHours = () => setHours((prev) => (prev % 12) + 1);
    const decrementHours = () => setHours((prev) => ((prev - 2 + 12) % 12) + 1);
    const incrementMinutes = () => setMinutes((prev) => (prev + (incrementMints || 1)) % 60);
    const decrementMinutes = () => setMinutes((prev) => (prev - (incrementMints || 1) + 60) % 60);
    const toggleAMPM = () => setIsPM((prev) => !prev);

    return (
        <Popover>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    className={cn(
                        "w-full justify-start text-left font-normal",
                        !date && "text-slate-500"
                    )}
                >
                    <Clock className="mr-2 h-4 w-4 text-slate-500" />
                    {`${hours.toString().padStart(2, "0")}:${minutes
                        .toString()
                        .padStart(2, "0")} ${isPM ? "PM" : "AM"}`}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
                <div className="p-4 bg-white border border-slate-200 rounded-lg">
                    <div className="flex justify-center items-center space-x-4">
                        <div className="flex flex-col items-center">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={incrementHours}
                                className="text-slate-700 hover:bg-slate-100"
                            >
                                ▲
                            </Button>
                            <div className="text-4xl font-bold text-slate-900">
                                {hours.toString().padStart(2, "0")}
                            </div>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={decrementHours}
                                className="text-slate-700 hover:bg-slate-100"
                            >
                                ▼
                            </Button>
                        </div>
                        <div className="text-4xl font-bold text-slate-900">:</div>
                        <div className="flex flex-col items-center">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={incrementMinutes}
                                className="text-slate-700 hover:bg-slate-100"
                            >
                                ▲
                            </Button>
                            <div className="text-4xl font-bold text-slate-900">
                                {minutes.toString().padStart(2, "0")}
                            </div>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={decrementMinutes}
                                className="text-slate-700 hover:bg-slate-100"
                            >
                                ▼
                            </Button>
                        </div>
                        <div className="flex flex-col items-center">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={toggleAMPM}
                                className="text-slate-700 hover:bg-slate-100"
                            >
                                {isPM ? "PM" : "AM"}
                            </Button>
                        </div>
                    </div>
                </div>
            </PopoverContent>
        </Popover>
    );
};

export default TimePicker;
