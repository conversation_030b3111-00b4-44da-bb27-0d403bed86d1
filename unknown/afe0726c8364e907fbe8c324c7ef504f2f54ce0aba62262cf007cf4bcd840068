const CALoader = () => {
    return (
        <>
            <div className='h-screen flex justify-center items-center'>
                <div className="relative flex items-center justify-center w-32 h-32">
                    {/* Outer container with shadow */}
                    <div className="absolute w-full h-full rounded-full shadow-lg bg-primary">
                        {/* Rotating border element */}
                        <div className="absolute inset-0 rounded-full border-8 border-transparent border-t-secondary animate-spin"></div>
                    </div>

                    {/* Logo container with inner shadow */}
                    <div className="relative w-24 h-24 rounded-full bg-primary shadow-inner flex items-center justify-center">
                        <img
                            src={"/connectathlete-logo.svg"}
                            alt="ConnectAthlete Logo"
                            className="w-20 h-20 object-contain"
                        />
                    </div>
                </div>
            </div>
        </>
    )
}
export default CALoader