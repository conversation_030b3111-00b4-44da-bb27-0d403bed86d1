'use client';

import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import CALoader from './common/CALoader';

export const RouteLoader = () => {
    const pathname = usePathname();
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        setLoading(true);

        const timeout = setTimeout(() => {
            setLoading(false);
        }, 500);

        return () => clearTimeout(timeout);
    }, [pathname]);

    if (!loading) return null;

    return (
        <CALoader />
    );
};