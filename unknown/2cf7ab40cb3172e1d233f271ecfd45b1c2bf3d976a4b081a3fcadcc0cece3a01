import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from "@/components/ui/table"
import { RootState } from "@/store"
import { format } from "date-fns"
import { useSelector } from "react-redux"

interface IProps {
    sportName: string
}

const ViewScoreCard = ({ sportName }: IProps) => {
    const { scoreCardId, addedStatsScoreCardList } = useSelector((state: RootState) => state.coachSport)
    const scoreCard = addedStatsScoreCardList?.find(each => each?.id === scoreCardId)

    return (
        <>
            <div className="flex flex-col gap-4 justify-center items-center bg-slate-200 rounded-lg p-5">
                <h3 className="text-lg font-bold">View Of Score Card</h3>
                <div className="flex flex-col items-center gap-5">
                    <p className="font-semibold text-secondary">{sportName}</p>
                    <p><span className="font-semibold">As of:</span> {scoreCard?.statsAsOf && format(new Date(scoreCard?.statsAsOf), 'MMM, dd yyyy')}</p>
                    <p><span className="font-semibold">Athletes Trained :</span> {scoreCard?.athletesTrained}</p>

                    <div className="border border-slate-300 rounded-lg overflow-hidden">
                        <Table>
                            <TableHeader>
                                <TableRow className="text-lg  border-b border-slate-300">
                                    <TableHead className="text-wrap font-bold border-r border-slate-300">Athletes Placed in College/Pro</TableHead>
                                    <TableHead className="font-bold text-center text-secondary border-r border-slate-300">{scoreCard?.athletesTrained}</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {scoreCard?.athletePlacements?.map(each => (
                                    <TableRow key={each?.id} className="border-b border-slate-300">
                                        <TableCell className="text-wrap border-r border-slate-300">
                                            {each?.placement}
                                        </TableCell>
                                        <TableCell className="text-center border-r border-slate-300">
                                            {each?.athletesTrained}
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>

                    {scoreCard?.toggleChampionshipWon && scoreCard?.teamChampionshipWon &&
                        <p><span className="font-semibold">Team Champions Won:</span> {scoreCard?.teamChampionshipWon}</p>
                    }
                    {scoreCard?.toggleAthleteImprovementRate && scoreCard?.athleteImprovementRate &&
                        <p><span className="font-semibold">Athlete Improvement Rate:</span> {scoreCard?.athleteImprovementRate}</p>
                    }
                    {scoreCard?.toggleRetentionRate && scoreCard?.athleteRetentionRate &&
                        <p><span className="font-semibold">Athlete Retention Rate:</span> {scoreCard?.athleteRetentionRate}</p>
                    }
                    {scoreCard?.toggleCertificationEarned && scoreCard?.certificationsEarned &&
                        <p><span className="font-semibold">Certifications Earned:</span> {scoreCard?.certificationsEarned}</p>
                    }
                    {scoreCard?.description && <p>{scoreCard?.description}</p>}
                </div>
            </div>
        </>
    )
}
export default ViewScoreCard