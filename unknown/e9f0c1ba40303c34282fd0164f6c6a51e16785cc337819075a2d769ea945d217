// types/next-auth.d.ts
import NextA<PERSON>, { DefaultSession, DefaultUser } from "next-auth";
import { JWT } from "next-auth/jwt";

declare module "next-auth" {
  interface Session {
    user: {
      email: string;
      userLastName: string;
      userFirstName: string;
      token: any;
      user?: any;
      id: string;
      userRole?: number;
      roleId: number;
      profileData?: any;
      galleryData?: any;
    } & DefaultSession["user"];
  }

  interface User {
    email: string;
    userLastName: string;
    userFirstName: string;
    token: any;
    user?: any;
    id: string;
    userRole?: number;
    roleId: number;
    profileData?: any;
    galleryData?: any;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    email: string;
    userLastName: string;
    userFirstName: string;
    token: any;
    user?: any;
    id: string;
    userRole?: number;
    roleId: number;
    profileData?: any;
    galleryData?: any;
  }
}
export interface SignInResponse {
  email: string;
  userLastName: string;
  userFirstName: string;
  token: any;
  user?: any;
  id: string;
  roleId: string;
  profileData: any;
  galleryData: any;
}
