import CommonCalender from "@/components/common/CommonCalender"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from "@/components/ui/table"
import { RootState } from "@/store"
import { handleCoachSportInputChange } from "@/store/slices/coach/coachSportSlice"
import { format } from "date-fns"
import { PencilLine, Trash2 } from "lucide-react"
import { useDispatch, useSelector } from "react-redux"

const CoachScoreCard = () => {
    const { scoreCardDate, addedStatsScoreCardList, } = useSelector((state: RootState) => state.coachSport)
    const dispatch = useDispatch()

    const handleOnChange = (name: string, value: boolean) => {
        dispatch(handleCoachSportInputChange({ name, value }))
    }

    const handleViewScoreCard = (id: number) => {
        dispatch(handleCoachSportInputChange({ name: 'scoreCardId', value: id }))
    }

    return (
        <>
            <div className="bg-slate-100 rounded-lg p-5 flex flex-col items-center gap-5">
                <h3 className="text-xl font-bold">Coach Score Card</h3>
                <div className="w-full md:w-52 text-center">
                    <Label>Snapshot Date</Label>
                    <CommonCalender
                        mode="single"
                        placeholder="Pick a date"
                        dateValue={scoreCardDate}
                        setDateFn={(date) => handleOnChange('scoreCardDate', date)}
                    />
                </div>

                <div className="border border-slate-300 rounded-lg overflow-hidden">
                    <Table>
                        <TableHeader>
                            <TableRow className="text-lg border-b border-slate-300">
                                <TableHead className="font-bold border-r border-slate-300">As Of Date</TableHead>
                                <TableHead className="font-bold text-center border-r border-slate-300">View Scorecard</TableHead>
                                <TableHead className="font-bold text-center">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {addedStatsScoreCardList?.map((each) => (
                                <TableRow key={each?.id} className="border-b border-slate-300">
                                    <TableCell className="text-wrap border-r border-slate-300">
                                        {each?.statsAsOf && format(new Date(each?.statsAsOf), 'MMM dd, yyyy')}
                                    </TableCell>
                                    <TableCell className="text-center border-r border-slate-300">
                                        <Button variant="ghost" onClick={() => handleViewScoreCard(each?.id)}>View</Button>
                                    </TableCell>
                                    <TableCell className="text-center">
                                        <div className="flex items-center gap-4">
                                            <Button variant={'outline'} size={'icon'}>
                                                <PencilLine />
                                            </Button>
                                            <Button variant={'destructive'} size={'icon'}>
                                                <Trash2 />
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>

            </div>
        </>
    )
}
export default CoachScoreCard