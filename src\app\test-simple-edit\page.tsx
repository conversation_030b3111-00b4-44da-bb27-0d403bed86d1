'use client'
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store';
import { fetchCoachSocialMediaLinks, handleCoachInputChange } from '@/store/slices/coach/coachProfileSlice';
import SimpleSocialMediaEdit from '@/components/common/SimpleSocialMediaEdit';

const TestSimpleEditPage = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { coachSocialMediaList, toggleSocialMedia, loading } = useSelector((state: RootState) => state.coachProfile);

  useEffect(() => {
    dispatch(fetchCoachSocialMediaLinks());
  }, [dispatch]);

  const handleToggleSection = (checked: boolean) => {
    dispatch(handleCoachInputChange({
      name: 'toggleSocialMedia',
      value: checked
    }));
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Simple Social Media Edit Test</h1>
      
      <div className="mb-8 p-4 bg-blue-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Instructions</h2>
        <ul className="list-disc list-inside space-y-2 text-sm">
          <li>This is a simplified version of the editable social media component</li>
          <li>Click the pencil icon to edit any social media platform</li>
          <li>Update the URL and toggle visibility</li>
          <li>Click the green checkmark to save</li>
          <li>Click the X to cancel</li>
        </ul>
      </div>

      <SimpleSocialMediaEdit
        origin="coach"
        list={coachSocialMediaList}
        toggleSocialMediaSection={toggleSocialMedia}
        onChangeToggleSection={handleToggleSection}
        loading={loading}
        fetchLoading={loading}
      />

      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Raw Data</h2>
        <pre className="text-sm overflow-auto">
          {JSON.stringify(coachSocialMediaList, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default TestSimpleEditPage;
