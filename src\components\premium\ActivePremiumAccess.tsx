'use client'
import { usePaymentData } from "@/contexts/PaymentContext";
import { useTokenValues } from "@/hooks/useTokenValues";
import { AppDispatch, RootState } from "@/store";
import { fetchTransactionsHistory, resetPaymentStates } from "@/store/slices/premiumSlice";
import { ROLES } from "@/utils/constants";
import { EachTransaction } from "@/utils/interfaces";
import Link from "next/link";
import { notFound, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { Skeleton } from "../ui/skeleton";
import OrderSummaryItem from "./OrderSummaryItem";

const ActivePremiumAccess = ({ transactionId }: { transactionId: string }) => {
    const { transactionsList, apiStatus } = useSelector((state: RootState) => state.premium)
    const [latestTransaction, setLatestTransaction] = useState<EachTransaction | null>(null)
    const dispatch = useDispatch<AppDispatch>()
    const router = useRouter()
    const { roleId } = useTokenValues()
    const { clearPaymentData } = usePaymentData();

    useEffect(() => {
        if (!transactionId) {
            return notFound()
        }
    }, [router, transactionId])

    useEffect(() => {
        clearPaymentData()
        dispatch(resetPaymentStates())
    }, [])

    useEffect(() => {
        dispatch(fetchTransactionsHistory());
    }, [dispatch]);

    useEffect(() => {
        if (transactionsList!?.length > 0) {
            const latestTransactionHistory = transactionsList?.find((each) => each.isLatest)
            setLatestTransaction(latestTransactionHistory!)
        }
    }, [dispatch, transactionsList]);


    useEffect(() => {
        // Replace current history state (remove the payment page from back stack)
        window.history.replaceState(null, "", window.location.href);

        const handlePopState = () => {
            // On back button click -> redirect to
            router.replace(roleId === ROLES.ATHLETE ? "/athlete/premium-plan" : '/business/premium-plan');
        };

        window.addEventListener("popstate", handlePopState);

        return () => {
            window.removeEventListener("popstate", handlePopState);
        };
    }, [router]);


    return (
        <>
            <div className="flex flex-col gap-4">
                <div className="grid grid-cols-1 gap-6">
                    <Card className="p-6 flex flex-col items-center justify-center gap-8">
                        <h3 className='text-primary font-bold text-lg'>Your Premium Access is Active.</h3>
                        <p className="text-md font-semibold text-secondary text-center lg:text-left">
                            You’ll continue to enjoy all the exclusive features
                            that help you stay visible, connected, and ahead of
                            the game.
                        </p>

                        {apiStatus === 'fetchTransactionLoading' ?
                            <div className="w-full space-y-3  p-4 bg-white rounded-xl shadow-md">
                                {[...Array(8)].map((_, i) => (
                                    <Skeleton key={i + 'so'} className="h-6 w-full bg-slate-200 animate-pulse" />))}
                            </div>
                            :
                            <OrderSummaryItem item={latestTransaction!} />
                        }

                        <Link href={roleId === ROLES.ATHLETE ? `/athlete` : `/business`}>
                            <Button>Back to Home</Button>
                        </Link>
                    </Card>
                </div>
            </div >
        </>
    )
}
export default ActivePremiumAccess