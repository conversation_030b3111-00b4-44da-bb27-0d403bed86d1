// app/providers.tsx
"use client";

import { AuthProvider } from "@/components/AuthProvider";
import StateResetWatcher from "@/components/common/StateResetWatcher";
import { RouteLoader } from "@/components/RouteLoader";
import { PaymentProvider } from "@/contexts/PaymentContext";
import { SnackbarProvider } from "@/contexts/SnackbarContext";
import { ReduxProvider } from "@/store/provider";
import { SessionProvider } from "next-auth/react";
import { ReactNode } from "react";
import { ToastContainer } from "react-toastify";

export function Providers({ children }: { children: ReactNode }) {
    return (
        <SessionProvider>
            <ReduxProvider>
                <PaymentProvider>
                    <StateResetWatcher />
                    <SnackbarProvider>
                        <RouteLoader />
                        <AuthProvider>
                            {children}
                        </AuthProvider>
                    </SnackbarProvider>
                </PaymentProvider>
            </ReduxProvider >
            <ToastContainer position="top-center" />
        </SessionProvider>
    )
}
