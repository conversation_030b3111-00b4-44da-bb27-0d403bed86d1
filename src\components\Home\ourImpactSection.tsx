"use client";
import Image from "next/image";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useCountUp } from "@/hooks/useCountUp";
import { useInView } from "@/hooks/useInView";
import {
  titleParseOptions,
  shortDescriptionParseOptions,
} from "@/utils/parseOptions";
import parse, { domToReact } from "html-react-parser";

export default function OurImpactSection() {
  const { ref, isInView } = useInView<HTMLDivElement>(-100);
  const { ourImpactSection, ourImpactImages } = useSelector(
    (state: RootState) => state.homeCMS
  );

  // Limit to first 3 items
  const items = ourImpactImages?.slice(0, 3) || [];

  // Use static hooks up front (fixed at 3)
  const count0 = useCountUp(
    isInView ? parseInt(items[0]?.fileTitle || "0") : 0
  );
  const count1 = useCountUp(
    isInView ? parseInt(items[1]?.fileTitle || "0") : 0
  );
  const count2 = useCountUp(
    isInView ? parseInt(items[2]?.fileTitle || "0") : 0
  );

  const counts = [count0, count1, count2];

  return (
    <section
      id="registration"
      ref={ref}
      className="bg-[#0D1D3A] text-white py-10 px-3 sm:px-8 md:px-16 lg:px-36 xl:px-56 w-full"
    >
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-6">
          {parse(
            ourImpactSection?.title || "",
            titleParseOptions
          )}
        </h2>
        <p className="cms-text text-lg text-gray-300 leading-relaxed max-w-3xl mx-auto">
          {parse(
            ourImpactSection?.description ||
              "",
            shortDescriptionParseOptions
          )}
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8 text-center">
        {items.map((item, index) => (
          <div
            key={index}
            className="bg-[#13294B] p-6 rounded-2xl shadow-lg hover:shadow-orange-500/20 transition duration-300"
          >
            <div className="mb-4 flex justify-center">
              {item?.fileLocation ? (
                <Image
                  src={item.fileLocation}
                  alt={item?.fileDescription || "impact-image"}
                  width={80}
                  height={80}
                  className="rounded-full object-cover"
                />
              ) : (
                <div className="w-20 h-20 bg-gray-600 rounded-full" />
              )}
            </div>
            <div className="text-4xl font-bold mb-2 text-white">
              {counts[index]?.toLocaleString?.() || 0}+
            </div>
            <div className="text-gray-400 text-sm uppercase tracking-wide">
              {item?.fileDescription || "Impact"}
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
