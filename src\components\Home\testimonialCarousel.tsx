"use client";
import { useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { ArrowLeft, ArrowRight } from "lucide-react";
import parse, { domToReact } from "html-react-parser";
import {
  titleParseOptions,
  shortDescriptionParseOptions,
  fileTitleParseOptions,
  fileDescriptionParseOptions,
  fileTestimonialTitleParseOptions
} from "@/utils/parseOptions";

export default function TestimonialCarousel() {
  const { testimonialSection, testimonialImages, loading } = useSelector(
    (state: RootState) => state.homeCMS
  );

  const [current, setCurrent] = useState(0);

  const hasTestimonials = testimonialImages.length > 0;

  const next = () =>
    setCurrent((prev) => (prev + 1) % testimonialImages.length);
  const prev = () =>
    setCurrent(
      (prev) => (prev - 1 + testimonialImages.length) % testimonialImages.length
    );

  if (loading || !testimonialSection) return null;

  return (
    <section className="bg-[#0D1D3A] text-white py-16 px-4 sm:px-8 md:px-16 lg:px-36 xl:px-56 w-full">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-4">
          {parse(testimonialSection.title || "", titleParseOptions)}
        </h2>
        <p className="text-lg text-gray-300 max-w-3xl mx-auto">
          {parse(
            testimonialSection.description || "",
            shortDescriptionParseOptions
          )}
        </p>
      </div>

      <div className="flex justify-center items-center gap-4">
        <button
          onClick={prev}
          className="p-2 rounded-full hover:bg-white/10 transition"
        >
          <ArrowLeft className="w-6 h-6 text-white" />
        </button>

        <div className="bg-[#112446] text-white rounded-xl p-6 max-w-xl w-full shadow-lg">
          {hasTestimonials ? (
            <div className="text-center">
              {testimonialImages[current].fileLocation ? (
                <img
                  src={testimonialImages[current].fileLocation}
                  alt={testimonialImages[current].fileTitle}
                  className="w-16 h-16 rounded-full mx-auto mb-4 object-cover"
                />
              ) : (
                <div className="w-16 h-16 rounded-full bg-blue-600 mx-auto mb-4 flex items-center justify-center text-xl font-bold">
                  {testimonialImages[current].fileTitle?.charAt(0)}
                </div>
              )}
              <p className="text-sm text-gray-300">
                —{" "}
                {parse(
                  testimonialImages[current].fileTitle || "",
                  fileTestimonialTitleParseOptions
                )}
              </p>
              <p className="text-xl italic mb-4">
                “
                {parse(
                  testimonialImages[current].fileDescription || "",
                  fileDescriptionParseOptions
                )}
                ”
              </p>
            </div>
          ) : (
            <div className="text-center">
              <p className="text-xl italic mb-4">
                “We created Connect Athlete to give every aspiring athlete a
                real path forward. We’re just getting started—and we can’t wait
                to share the stories that are unfolding.”
              </p>
              <p className="text-sm text-gray-300">— Buvana, Founder</p>
            </div>
          )}
        </div>

        <button
          onClick={next}
          className="p-2 rounded-full hover:bg-white/10 transition"
        >
          <ArrowRight className="w-6 h-6 text-white" />
        </button>
      </div>
    </section>
  );
}
