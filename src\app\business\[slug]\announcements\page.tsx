"use client";

import { useParams } from "next/navigation";
import { useEffect } from "react";
import SportsBusiness from "@/components/announcement-dashboard/index";
import ClientGuard from "@/components/ClientGuard";
import { useTokenValues } from "@/hooks/useTokenValues";

const BusinessAnnouncementsPage = () => {
  const params = useParams();
  const {roleId} = useTokenValues()
  const slug = params.slug as string;

  // Extract business name and user ID from slug
  // Expected format: "BusinessName-UserID"
  const extractBusinessInfo = (slug: string) => {
    if (!slug) return { businessName: "", userId: "" };
    
    const lastDashIndex = slug.lastIndexOf('-');
    if (lastDashIndex === -1) return { businessName: slug, userId: "" };
    
    const businessName = slug.substring(0, lastDashIndex);
    const userId = slug.substring(lastDashIndex + 1);
    
    return { businessName, userId };
  };

  const { businessName, userId } = extractBusinessInfo(slug);

  useEffect(() => {
    // You can use businessName and userId here for any specific logic
  }, [businessName, userId]);

  if (!roleId) return null;

  return (
    <ClientGuard allowedRoles={[4]}>
      <div className="min-h-screen">
        {/* Optional: Display business info */}
                    <div className="flex justify-center bg-white rounded-lg px-4 py-2 shadow-sm border border-gray-200 flex items-center gap-1">
              <span className="text-gray-600">www.connectathlete.com/</span>
              <span className="text-blue-600 font-semibold">
                {businessName}/
              </span>
              <span className="text-gray-600">{userId}-</span>
              <span className="text-blue-600 font-medium">Announcements</span>
            </div>
        
        {/* Announcements Dashboard Component */}
        <SportsBusiness />
      </div>
    </ClientGuard>
  );
};

export default BusinessAnnouncementsPage;
