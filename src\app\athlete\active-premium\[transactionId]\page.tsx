import ClientGuard from "@/components/ClientGuard"
import ActivePremiumAccess from "@/components/premium/ActivePremiumAccess"
import { ROLES } from "@/utils/constants"
import { Params } from "next/dist/shared/lib/router/utils/route-matcher"

const AthleteActivePremiumPage = ({ params }: { params: Params }) => {
    const { transactionId } = params

    return (
        <ClientGuard allowedRoles={[ROLES.ATHLETE]}>
            <>
                <div className="flex flex-col gap-4">
                    <h2 className="text-primary text-2xl font-bold text-center">
                        Premium Access
                    </h2>
                    <ActivePremiumAccess transactionId={transactionId} />
                </div>
            </>
        </ClientGuard>
    )
}
export default AthleteActivePremiumPage