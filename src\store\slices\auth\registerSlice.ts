import { createSlice, PayloadAction, createAsyncThunk } from "@reduxjs/toolkit";

const apiUrl = "https://api.engageathlete.com/api/user-profile/v2/";

interface FormData {
    [key: string]: string;
}

interface Errors {
    [key: string]: string;
}

export interface RegisterState {
    formUserType: "athlete" | "coach" | "business" | null;
    roleId: number | null;
    userId: number | null;
    formData: { [key: string]: string };
    errors: { [key: string]: string };
    agreed: boolean;
    loading: boolean;
    success: boolean;
    error: string | null;
}

const initialState: RegisterState = {
    formUserType: null,
    roleId: null,
    userId: null,
    formData: {},
    errors: {},
    agreed: false,
    loading: false,
    success: false,
    error: null,
};

export const fetchRoleId = createAsyncThunk(
  "register/fetchRoleId",
  async (roleType: string, { rejectWithValue }) => {
    try {
      const res = await fetch(`${apiUrl}roles`);
      const result = await res.json();
        
      // Normalize roleType: map 'business' to 'academy/club'
      const normalizedRoleType =
        roleType === "business" ? "business" : roleType;

      const role = result.data.find(
        (r: any) => r.roleType === normalizedRoleType
      );

      if (!role) throw new Error("Role not found");
      return role.id;
    } catch (err: any) {
      return rejectWithValue(err.message);
    }
  }
);


export const signupUser = createAsyncThunk(
    "register/signupUser",
    async (
        {
            roleType,
            roleId,
            formData,
        }: { roleType: string; roleId: number; formData: FormData },
        { rejectWithValue }
    ) => {
        try {
            const payload = { ...formData, roleType, roleId };
            const res = await fetch(`${apiUrl}signup`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(payload),
            });
            const data = await res.json();
            if (!res.ok) return rejectWithValue(data.message || "Signup failed");
            return data;
        } catch (err: any) {
            return rejectWithValue(err.message);
        }
    }
);

export const verifyOtp = createAsyncThunk(
    "register/verifyOtp",
    async (
        {
            userId,
            roleId,
            otp,
        }: { userId: number; roleId: number; otp: string },
        { rejectWithValue }
    ) => {
        try {
            const res = await fetch(`https://api.engageathlete.com/api/user-profile/v2/verifyOTP`, {
                method: "PUT",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ userId, roleId, otp }),
            });
            const data = await res.json();
            if (!res.ok) return rejectWithValue(data.message || "Verification failed");
            return data;
        } catch (err: any) {
            return rejectWithValue(err.message);
        }
    }
);

export const resendOtp = createAsyncThunk(
    "register/resendOtp",
    async (
        { userId, roleId }: { userId: number; roleId: number },
        { rejectWithValue }
    ) => {
        try {
            const res = await fetch(`https://api.engageathlete.com/api/user-profile/v1/resendOTP`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ userId, roleId }),
            });
            const data = await res.json();
            if (!res.ok) return rejectWithValue(data.message || "Failed to resend OTP");
            return data;
        } catch (err: any) {
            return rejectWithValue(err.message);
        }
    }
);

const registerSlice = createSlice({
    name: "register",
    initialState,
    reducers: {
        setFormUserType(state, action: PayloadAction<RegisterState["formUserType"]>) {
            state.formUserType = action.payload;
            state.formData = {};
            state.errors = {};
            state.agreed = false;
        },
        updateFormData(state, action: PayloadAction<{ name: string; value: string }>) {
            const { name, value } = action.payload;
            state.formData[name] = value;
        },
        setErrors(state, action: PayloadAction<Errors>) {
            state.errors = action.payload;
        },
        clearErrors(state) {
            state.errors = {};
        },
        setAgreed(state, action: PayloadAction<boolean>) {
            state.agreed = action.payload;
        },
        resetForm(state) {
            state.formData = {};
            state.errors = {};
            state.agreed = false;
            state.formUserType = null;
            state.loading = false;
            state.success = false;
            state.error = null;
            state.userId = null;
            state.roleId = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchRoleId.fulfilled, (state, action) => {
                state.roleId = action.payload;
            })
            .addCase(signupUser.fulfilled, (state, action) => {
                
                state.userId = action.payload.data.userId; //
                state.roleId = action.payload.data.roleId;
                state.success = true;
            })
            .addCase(verifyOtp.fulfilled, (state) => {
                state.success = true;
            });
    },
});

export const {
    setFormUserType,
    updateFormData,
    setErrors,
    clearErrors,
    setAgreed,
    resetForm,
} = registerSlice.actions;

export default registerSlice.reducer;
