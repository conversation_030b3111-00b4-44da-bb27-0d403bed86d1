'use client'
import { Button } from '@/components/ui/button';
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Option } from '@/utils/interfaces';
import { ChevronDown } from 'lucide-react';
import React, { useState } from 'react';
import { Controller } from 'react-hook-form';
import Chip from './Chip';

interface MultiSelectWithChipProps {
    name?: string;
    control?: any;
    value?: Option[];
    onChange?: (value: Option[]) => void;
    options: Option[];
    maxSelect?: number;
    errors?: Record<string, any>;
    placeholder?: string;
    className?: string;
    disable?: boolean;
}

const MultiSelectWithChip: React.FC<MultiSelectWithChipProps> = ({
    name = 'field',
    control,
    value,
    onChange,
    options,
    maxSelect,
    errors,
    placeholder = 'Select options...',
    className,
    disable,
}) => {
    const [open, setOpen] = useState(false);

    const renderUI = (
        selected: Option[],
        handleChange: (val: Option[]) => void
    ) => {
        const handleSelect = (val: number) => {
            const item = options.find((o) => o.value === val);
            const isAlreadySelected = selected.some((s) => s.value === val);
            const hasReachedLimit =
                typeof maxSelect === 'number' && selected.length >= maxSelect;

            if (item && !isAlreadySelected && !hasReachedLimit) {
                handleChange([...selected, item]);
                setOpen(false);
            }
        };

        const handleRemove = (val: number) => {
            handleChange(selected.filter((s) => s.value !== val));
        };

        const filteredOptions = options && options.length >0 && options.filter(
            (opt) => !selected.some((s) => s.value === opt.value)
        );

        return (
            <div className={`flex flex-col gap-4 ${className}`}>
                <>
                    <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild disabled={disable}>
                            <Button
                                disabled={disable}
                                variant="outline"
                                role="combobox"
                                className="w-full justify-between bg-white hover:bg-white"
                            >
                                {placeholder || 'Select option(s)...'}
                                <ChevronDown className="text-slate-400" />
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent align="start" className="w-full p-0">
                            <Command>
                                <CommandInput placeholder="Search..." />
                                <CommandList>
                                    <CommandEmpty>No options found.</CommandEmpty>
                                    <CommandGroup>
                                        {filteredOptions && filteredOptions.length>0 && filteredOptions?.map((opt) => (
                                            <CommandItem
                                                key={opt.value}
                                                value={String(opt.label) || ''}
                                                className='capitalize'
                                                onSelect={() => handleSelect(opt.value)}
                                                disabled={disable}
                                            >
                                                {opt.label}
                                            </CommandItem>
                                        ))}
                                    </CommandGroup>
                                </CommandList>
                            </Command>
                        </PopoverContent>
                    </Popover>
                    {errors?.[name] && (
                        <p className="text-red-500 text-sm">
                            {errors[name]?.message}
                        </p>
                    )}
                </>

                <div className="flex flex-wrap gap-2">
                    {selected?.length > 0 && selected?.map((s) => (
                        <Chip
                            key={s.value}
                            id={s.value}
                            label={s.label}
                            onRemove={() => handleRemove(s.value)}
                        />
                    ))}
                </div>
            </div>
        );
    };

    // If using react-hook-form
    if (control && name) {
        return (
            <Controller
                name={name}
                control={control}
                disabled={disable}
                render={({ field }) =>
                    renderUI(field.value || [], (updated: Option[]) => {
                        field.onChange(updated);
                        if (onChange) onChange(updated);
                    })
                }
            />
        );
    }

    // Non-hook-form fallback
    return renderUI(value || [], onChange || (() => { }));
};

export default MultiSelectWithChip;
