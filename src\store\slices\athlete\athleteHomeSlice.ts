import axiosInstance from "@/utils/axiosInstance";
import { AthleteHomeStateModal } from "@/utils/interfaces";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

const initialState: AthleteHomeStateModal = {
  generalAnnouncements: [],
  sportsAnnouncements: [],
  apiStatus: "",
};

export const fetchGeneralSportsAnnouncements = createAsyncThunk(
  "announcements/fetchGeneralSportsAnnouncements",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const roleId = Number(localStorage.getItem("roleId"));
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_NOTIFICATION_API_URL}/getallannouncement/${roleId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data?.announcementWithSports ?? []);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

const athleteHomeSlice = createSlice({
  name: "athleteHome",
  initialState,
  reducers: {
    handleAthleteHomeUserUpdates: (state, action) => {
      const { name, value } = action.payload;
      state[name] = value;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchGeneralSportsAnnouncements.pending, (state) => {
        state.apiStatus = "announcementsLoading";
      })
      .addCase(fetchGeneralSportsAnnouncements.fulfilled, (state, action) => {
        state.apiStatus = "announcementsSuccess";
        const generalAnnouncements = action.payload?.filter(
          (item) => item?.announcement?.sportsId === "general"
        );
        const sportsAnnouncements = action.payload?.filter(
          (item) => item?.announcement?.sportsId !== "general"
        );
        state.sportsAnnouncements = sportsAnnouncements;
        state.generalAnnouncements = generalAnnouncements;
      })
      .addCase(fetchGeneralSportsAnnouncements.rejected, (state, action) => {
        state.apiStatus = "announcementsFailed";
        state.sportsAnnouncements = [];
        state.generalAnnouncements = [];
      });
  },
});

export const { handleAthleteHomeUserUpdates } = athleteHomeSlice.actions;
export default athleteHomeSlice.reducer;
