import { X } from "lucide-react";

interface IProps {
    id: number | string;
    label: string;
    onRemove?: (id: string | number) => void
}

const Chip = ({ id, label, onRemove }: IProps) => {
    return (
        <>
            <div className="bg-secondary text-white rounded-3xl flex items-center justify-center font-bold gap-0 p-1 px-2">
                <span className="text-sm capitalize">{label}</span>
                {onRemove && <X className="cursor-pointer h-4" onClick={() => onRemove(id)} />}
            </div>
        </>
    )
}
export default Chip