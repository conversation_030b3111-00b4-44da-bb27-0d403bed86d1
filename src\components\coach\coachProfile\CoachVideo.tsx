
import UploadFiles from "@/components/common/UploadFiles"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { AppDispatch, RootState } from "@/store"
import { editCoachProfile, handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { fileUploadToS3 } from "@/store/slices/commonSlice"
import { useDispatch, useSelector } from "react-redux"
import { toast } from "react-toastify"
import { useState } from "react"

const CoachVideo = () => {
    const { toggleAboutVideo, aboutVideoFile } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch<AppDispatch>()

    // const { userId, roleId } = useTokenValues()

    const [videoFomData, setVideoFormData]=useState(null)

    const handleToggleSection = () => {
        dispatch(handleCoachInputChange({ name: 'toggleAboutVideo', value: !toggleAboutVideo }))
    }

    const handleChangeVideoFile = async (name, file) => {
        if (name === 'add') {
            dispatch(handleCoachInputChange({ name: 'aboutVideoFile', value: file }))

        } else if (name === 'remove') {
            dispatch(handleCoachInputChange({ name: 'aboutVideoFile', value: null }))
        }
        
    }

    const handleCoachIntroVideo = async () => {
        if (!aboutVideoFile) {
            toast.error("Please select a video file first");
            return;
        }

        const payload = {
            // Add required fields based on your API structure
            coachIntroVideo: aboutVideoFile, // or the S3 URL from upload
            // Add other required fields like roleId, userId, etc.
        };

        try {
            // Replace with your actual API endpoint for saving coach video
            const resultAction = await dispatch(editCoachProfile(payload));
            if (editCoachProfile.fulfilled.match(resultAction)) {
                toast.success("Video saved successfully");
                // Reset form or update UI as needed
            }
        } catch (error) {
            console.error("Failed to save video:", error);
            toast.error("Failed to save video");
        }
    };

    return (
        <>
            <div className="flex flex-col gap-5 bg-slate-100 rounded-lg p-4">
                <div className="flex items-center justify-center gap-5">
                    <h2 className="text-primary text-lg font-bold">About John</h2>
                    <Switch checked={toggleAboutVideo} onCheckedChange={handleToggleSection} />
                </div>
                {toggleAboutVideo ?
                    <UploadFiles
                        acceptType={["video/mp4"]}
                        value={aboutVideoFile}
                        onFileSelect={(file) => handleChangeVideoFile('add', file)}
                        handleRemove={() => handleChangeVideoFile('remove', null)}
                        className="w-full"
                    /> :
                    null
                }

                {aboutVideoFile && <div className="flex justify-end">
                    <Button onClick={() => handleCoachIntroVideo()}>Save</Button>
                </div>}
            </div>
        </>
    )
}
export default CoachVideo
