import ClientGuard from "@/components/ClientGuard"
import Checkout from "@/components/premium/Checkout"
import { ROLES } from "@/utils/constants"
import { Params } from "next/dist/shared/lib/router/utils/route-matcher"
import { notFound } from "next/navigation"

const BusinessCheckoutPage = ({ params }: { params: Params }) => {
    const { planId } = params

    if (!planId) return notFound()

    return (
        <ClientGuard allowedRoles={[ROLES.BUSINESS]}>
            <div className=" flex flex-col gap-5">
                <h2 className="text-primary text-2xl font-bold text-center">Checkout</h2>
                <Checkout planId={planId} />
            </div>
        </ClientGuard>
    )
}
export default BusinessCheckoutPage