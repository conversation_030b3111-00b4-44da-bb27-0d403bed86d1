import SearchAndSortHeader from "@/components/adminSpecials/SearchAndSortHeader"
import ClientGuard from "@/components/ClientGuard"
import CoachCardItem from "@/components/coach/SearchCoach/CoachCardItem"
import CoachFilterPanel from "@/components/coach/SearchCoach/CoachFilterPanel"


const ExploreCoaches = () => {
    return (
        <>
            <ClientGuard allowedRoles={[2]}>
                <div className="min-h-screen bg-gray-50 p-4">
                    <div className="max-w-7xl mx-auto flex flex-col md:flex-row md:space-x-6">
                        {/* Left Filter Panel */}
                        <aside className="md:w-1/4 mb-6 md:mb-0">
                            <CoachFilterPanel />
                        </aside>

                        {/* Main Feed */}
                        <main className="md:w-3/4 space-y-6 p-5 sm:px-4 bg-white rounded-lg">
                            <div className="flex items-center">
                                <h2 className="text-lg md:text-3xl text-center w-full font-bold text-blue-900">Search Coaches</h2>
                            </div>
                            <SearchAndSortHeader />
                            {Array(4).fill("")?.map((item) => (
                                <CoachCardItem />
                            ))}
                        </main>
                    </div>
                </div>
            </ClientGuard>
        </>
    )
}
export default ExploreCoaches