import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import GovtIdStepOne from "./GovtIdStepOne"
import VerificationStepTwo from "./VerificationStepTwo"
import { useDispatch, useSelector } from "react-redux"
import { postAddGovtDocument } from "@/store/slices/coach/coachProfileSlice"
import { AppDispatch, RootState } from "@/store"
import { useTokenValues } from "@/hooks/useTokenValues"
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"
import { toast } from "react-toastify"
import { useState } from "react"
import { CheckCircle } from "lucide-react"

const CoachVerification = () => {
    const dispatch = useDispatch<AppDispatch>()
    const { govtIdData, addedGovtIdData, additionalDocList, coachProfileData } = useSelector((state: RootState) => state.coachProfile)
    const { userId, roleId } = useTokenValues()
    const { profileId } = useLocalStoredInfo()
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [checkboxes, setCheckboxes] = useState({
        nameVisible: false,
        certificationValid: false,
        recognizedAuthority: false
    })

    const handleCheckboxChange = (field: keyof typeof checkboxes) => (checked: boolean) => {
        setCheckboxes(prev => ({ ...prev, [field]: checked }))
    }

    const handleSaveDocument = async () => {
        // Validate that we have the government document
        if (!addedGovtIdData && !govtIdData) {
            toast.error("Please fill out the government ID form first")
            return
        }

        const govtFormData = addedGovtIdData || govtIdData

        if (!govtFormData.title || !govtFormData.description || !govtFormData.documentLink || !govtFormData.documentType || !govtFormData.file) {
            toast.error("Please fill out all required fields in government document")
            return
        }

        // Validate that at least one additional document is filled with all required fields
        const filledAdditionalDocs = additionalDocList.filter(doc =>
            doc.title &&
            doc.description &&
            doc.documentLink &&
            doc.documentType &&
            doc.file &&
            doc.expirationDate
        )

        if (filledAdditionalDocs.length === 0) {
            toast.error("Please fill out at least one additional document")
            return
        }

        // Validate checkboxes
        if (!checkboxes.nameVisible || !checkboxes.certificationValid || !checkboxes.recognizedAuthority) {
            toast.error("Please check all the required boxes before saving")
            return
        }

        setIsSubmitting(true)

        try {
            // Create array payload with government document and additional documents
            const documentsArray: any[] = []

            // Add government document
            documentsArray.push({
                userId: Number(userId),
                roleId: Number(roleId),
                coachId: coachProfileData?.id || profileId || Number(userId),
                docTypeId: govtFormData.documentType!.value,
                docTypTxt: govtFormData.documentType!.label,
                docTitle: govtFormData.title,
                docExpirationDate: govtFormData.expirationDate ? new Date(govtFormData.expirationDate).toISOString() : null,
                docDesc: govtFormData.description || "",
                docLink: govtFormData.documentLink || "",
                docFilePath: govtFormData.file || "",
                isHidden: false
            })

            // Add additional documents (only filled ones)
            filledAdditionalDocs.forEach(doc => {
                if (doc.documentType) { // Additional safety check
                    documentsArray.push({
                        userId: Number(userId),
                        roleId: Number(roleId),
                        coachId: coachProfileData?.id || profileId || Number(userId),
                        docTypeId: doc.documentType.value,
                        docTypTxt: doc.documentType.label,
                        docTitle: doc.title,
                        docExpirationDate: doc.expirationDate ? new Date(doc.expirationDate).toISOString() : null,
                        docDesc: doc.description || "",
                        docLink: doc.documentLink || "",
                        docFilePath: doc.file || "",
                        isHidden: false
                    })
                }
            })

            const resultAction = await dispatch(postAddGovtDocument(documentsArray))

            if (postAddGovtDocument.fulfilled.match(resultAction)) {
                toast.success(`Documents saved successfully (${documentsArray.length} documents)`)
                // Navigate to Declaration or next step
            }
        } catch (error) {
            console.error("Failed to save documents:", error)
            toast.error("Failed to save documents")
        } finally {
            setIsSubmitting(false)
        }
    }

    return (
        <>
            <div className="flex flex-col bg-slate-100 rounded-lg p-5 gap-10">
                <h3 className="text-xl font-bold text-center">Coach Verification</h3>

                <div className="flex flex-col gap-2">
                    <h2 className="font-bold">
                        Step 1:  {" "}
                        <span className="text-secondary">Please Upload one Goverment ID</span>
                    </h2>
                    <GovtIdStepOne />
                </div>

                <div className="flex flex-col gap-2">
                    <h2 className="font-bold">
                        Step 2:  {" "}
                        <span className="text-secondary"> Please Upload atleast one Additional Document</span>
                    </h2>
                    <VerificationStepTwo />
                </div>

                <div className="flex flex-col gap-2">
                    <h2 className="font-bold">
                        Step 3:  {" "}
                        <span className="text-secondary">Please check these these boxes and then click
                            before uploading these documents</span>
                    </h2>

                    <div className="flex flex-col p-4 gap-8 rounded-xl">
                        <div className="flex items-center gap-2">
                            <Checkbox
                                className="border-slate-500"
                                checked={checkboxes.nameVisible}
                                onCheckedChange={(checked) => handleCheckboxChange('nameVisible')(!!checked)}
                            />
                            <Label>My name is clearly visible on this document.</Label>
                        </div>
                        <div className="flex items-center gap-2">
                            <Checkbox
                                className="border-slate-500"
                                checked={checkboxes.certificationValid}
                                onCheckedChange={(checked) => handleCheckboxChange('certificationValid')(!!checked)}
                            />
                            <Label>The certification is still valid.</Label>
                        </div>
                        <div className="flex items-center gap-2">
                            <Checkbox
                                className="border-slate-500"
                                checked={checkboxes.recognizedAuthority}
                                onCheckedChange={(checked) => handleCheckboxChange('recognizedAuthority')(!!checked)}
                            />
                            <Label>This was issued by a recognized authority.</Label>
                        </div>
                    </div>
                </div>

                {/* Document Summary */}
                <div className="flex flex-col gap-2">
                    <h2 className="font-bold">
                        Summary: {" "}
                        <span className="text-secondary">Documents to be submitted</span>
                    </h2>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="space-y-2">
                            {(addedGovtIdData || govtIdData) && (
                                <div className="flex items-center gap-2">
                                    <CheckCircle className="w-4 h-4 text-green-500" />
                                    <span className="text-sm">
                                        Government Document: {(addedGovtIdData || govtIdData)?.title || "Untitled"}
                                    </span>
                                </div>
                            )}
                            {additionalDocList.filter(doc =>
                                doc.title && doc.description && doc.documentLink && doc.documentType && doc.file && doc.expirationDate
                            ).map((doc, index) => (
                                <div key={index} className="flex items-center gap-2">
                                    <CheckCircle className="w-4 h-4 text-green-500" />
                                    <span className="text-sm">
                                        Additional Document {index + 1}: {doc.title}
                                    </span>
                                </div>
                            ))}
                        </div>
                        <div className="mt-3 pt-3 border-t border-blue-200">
                            <p className="text-sm text-blue-700 font-medium">
                                Total Documents: {1 + additionalDocList.filter(doc =>
                                    doc.title && doc.description && doc.documentLink && doc.documentType && doc.file && doc.expirationDate
                                ).length}
                            </p>
                        </div>
                    </div>
                </div>

                <div className="flex items-center justify-center">
                    <Button onClick={handleSaveDocument} disabled={isSubmitting}>
                        {isSubmitting ? "Saving Documents..." : "Confirm & Save All Documents"}
                    </Button>
                </div>
            </div>
        </>
    )
}
export default CoachVerification