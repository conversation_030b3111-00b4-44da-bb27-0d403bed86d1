"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import Link from "next/link";

interface PostProps {
  author: string;
  role: string;
  content: string;
  time: string;
  avatar: string;
  date: string;
}

export default function SpecialItem({
  author,
  role,
  content,
  time,
  avatar,
  date,
}: PostProps) {

  return (
    <Link href={`/admin-specials/specials/${encodeURIComponent(author.toLowerCase().replace(/\s+/g, "-"))}`}
      className="block">
      <Card className="w-full">
        <CardHeader className="flex flex-row items-center space-x-4">
          <img
            src={avatar}
            alt={`${author} profile`}
            className="w-12 h-12 rounded-full object-cover"
          />
          <div className="flex flex-col">
            <CardTitle className="text-base">{author}</CardTitle>
            <CardDescription>{role}</CardDescription>
          </div>
        </CardHeader>

        <CardContent>
          <p className="text-gray-700 whitespace-pre-line">{content}</p>
        </CardContent>

        <CardFooter className="flex flex-col sm:flex-row justify-between items-center text-sm text-gray-500 space-y-1 sm:space-y-0">
          <span>
            {time} · {date}
          </span>

          <div className="flex space-x-4">
            <a
              href={'#'}
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Share on LinkedIn"
              className="hover:text-blue-700"
            >
              🔗
            </a>
            <a
              href={'#'}
              target="_blank"
              rel="noopener noreferrer"
              aria-label="Share on Facebook"
              className="hover:text-blue-600"
            >
              📘
            </a>
          </div>
        </CardFooter>
      </Card>
    </Link>
  );
}
