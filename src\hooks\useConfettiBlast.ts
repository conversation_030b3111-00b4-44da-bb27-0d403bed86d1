"use client";
import confetti from "canvas-confetti";

export function useConfettiBlast() {
  const fireConfetti = (elementId: string) => {
    const element = document.getElementById(elementId);
    if (!element) return;

    const rect = element.getBoundingClientRect();
    confetti({
      particleCount: 50,
      spread: 70,
      origin: {
        x: (rect.left + rect.width / 2) / window.innerWidth,
        y: (rect.top + rect.height / 2) / window.innerHeight,
      },
    });
  };

  return fireConfetti;
}
