import { useEffect, useRef, useState } from "react";
import { FaEye } from "react-icons/fa";
import { TbCertificate } from "react-icons/tb";

const dummyPrograms = new Array(10).fill(0).map((_, i) => ({
  id: i,
  title: `Alcohol PREVENTION PROGRAM ${i + 1}`,
  blurb: "A short program description.",
  completedOn: "6-10-2025",
  status: i % 2 === 0 ? "Completed" : "In-Progress",
}));

export default function AthleteResourcePage() {
  const [programs, setPrograms] = useState(dummyPrograms);
  const [page, setPage] = useState(1);
  const [activeTab, setActiveTab] = useState<"SCHOOL" | "GENERAL">("SCHOOL");
  const [search, setSearch] = useState("");
  const [showCert, setShowCert] = useState<null | number>(null);
  const scrollRef = useRef<HTMLDivElement>(null);

  const handleScroll = () => {
    const element = scrollRef.current;
    if (element) {
      const { scrollTop, scrollHeight, clientHeight } = element;
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        loadMore();
      }
    }
  };

  const loadMore = () => {
    const more = new Array(10).fill(0).map((_, i) => ({
      id: programs.length + i,
      title: `Alcohol PREVENTION PROGRAM ${programs.length + i + 1}`,
      blurb: "A short program description.",
      completedOn: "6-10-2025",
      status: (programs.length + i) % 2 === 0 ? "Completed" : "In-Progress",
    }));
    setPrograms((prev) => [...prev, ...more]);
    setPage((p) => p + 1);
  };

  const filteredPrograms = programs.filter((p) =>
    p.title.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="h-screen flex gap-4 max-w-screen-xl mx-auto overflow-hidden">
      {/* Filter Section */}
      <div className="w-1/4 h-[50vh] bg-white shadow p-4 rounded-lg border sticky top-0 h-full overflow-y-auto hover:shadow-blue-200 transition-shadow">
        <div className="mb-4">
          <label className="block mb-1">Program name</label>
          <select className="w-full border p-2 rounded">
            <option>All</option>
          </select>
        </div>
        <div className="mb-4">
          <label className="block mb-1">Program Dates</label>
          <input type="date" className="w-full border p-2 rounded" />
        </div>
        <div>
          <label className="block mb-1">Completion status</label>
          <select className="w-full border p-2 rounded">
            <option>All</option>
            <option>Completed</option>
            <option>In-Progress</option>
          </select>
        </div>
      </div>

      {/* Main Content Section */}
      <div className="w-2/4 flex flex-col">
        <div className="flex border-b mb-4 shrink-0">
          {["SCHOOL", "GENERAL"].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab as "SCHOOL" | "GENERAL")}
              className={`flex-1 py-2 font-semibold ${
                activeTab === tab ? "border-b-4 border-black" : ""
              }`}
            >
              {tab}
            </button>
          ))}
        </div>

        <div className="flex justify-between items-center mb-2 px-1">
          <input
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            placeholder="Search programs..."
            className="border px-3 py-1 rounded w-2/3"
          />
          <select className="border px-2 py-1 rounded">
            <option>Sort by relevance</option>
          </select>
        </div>

        <div
          ref={scrollRef}
          onScroll={handleScroll}
          className="overflow-y-auto space-y-4 pr-2 h-full"
        >
          {filteredPrograms.map((program) => (
            <div
              key={program.id}
              className="bg-white rounded-lg shadow p-4 border hover:shadow-blue-200 transition-shadow flex flex-col md:flex-row items-center justify-between"
            >
              <div>
                <h3 className="text-lg font-bold text-indigo-600">
                  {program.title}
                </h3>
                <p className="text-sm text-gray-500">{program.blurb}</p>
                <p className="text-xs text-gray-400 mt-1">
                  Completed on {program.completedOn}
                </p>
              </div>
              <div className="mt-2 md:mt-0 flex items-center gap-2">
                <span
                  className={`text-sm px-3 py-1 rounded-full ${
                    program.status === "Completed"
                      ? "bg-green-100 text-green-700"
                      : "bg-yellow-100 text-yellow-700"
                  }`}
                >
                  {program.status}
                </span>
                {program.status === "Completed" && (
                  <>
                    <FaEye
                      className="text-blue-500 cursor-pointer hover:scale-110"
                      onClick={() =>
                        alert(`Viewing details for ${program.title}`)
                      }
                    />
                    <TbCertificate
                      className="text-green-600 cursor-pointer hover:scale-110"
                      onClick={() => setShowCert(program.id)}
                    />
                  </>
                )}
              </div>
            </div>
          ))}
          <div className="text-center text-sm text-gray-400">
            Loading more...
          </div>
        </div>
      </div>

      {/* Announcement Section */}
      <div className="w-1/4 h-[50vh] bg-white shadow p-4 rounded-lg border sticky top-0 h-full overflow-y-auto hover:shadow-blue-200 transition-shadow">
        <div className="flex mb-4 border-b">
          <button className="flex-1 py-2 font-semibold border-b-2 border-blue-600 text-blue-600">
            General
          </button>
          <button className="flex-1 py-2 font-semibold">Sports</button>
        </div>
        <div className="space-y-4">
          <div className="bg-gray-100 p-4 rounded h-24"></div>
          <div className="bg-gray-100 p-4 rounded h-24"></div>
        </div>
      </div>

      {showCert !== null && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white p-6 rounded-lg w-[400px] text-center shadow-lg">
            <h2 className="text-xl font-bold mb-2">
              Certificate of Completion
            </h2>
            <p>You have successfully completed the program on : 6-10-2025</p>
            <p className="mt-2 text-indigo-600 font-semibold">
              {programs.find((p) => p.id === showCert)?.title}
            </p>
            <button
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              onClick={() => setShowCert(null)}
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
