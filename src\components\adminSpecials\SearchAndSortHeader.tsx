'use client';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from 'react';
import { FaSearch } from 'react-icons/fa';
import { IoClose } from 'react-icons/io5';

export default function SearchAndSortHeader() {
  const [search, setSearch] = useState('');
  const [sort, setSort] = useState('relevance');

  const handleClear = () => setSearch('');

  return (
    <div className="bg-white space-y-5 ">     
      <div className="flex justify-between items-center gap-4 flex-wrap px-1">
        <div className="relative">
          <input
            type="text"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            placeholder="Search..."
            className="border border-gray-300 rounded-lg pl-8 pr-6 py-2 text-sm w-64 focus:outline-none focus:ring focus:ring-blue-200"
          />
          <FaSearch className="absolute left-2.5 top-2.5 text-gray-500" />
          {search && (
            <button
              onClick={handleClear}
              className="absolute right-2 top-2.5 text-gray-500 hover:text-black"
              aria-label="Clear"
            >
              <IoClose />
            </button>
          )}
        </div>

        <Select onValueChange={(value) => setSort(value)} value={sort}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="relevance">Relevance</SelectItem>
            <SelectItem value="latest">Latest</SelectItem>
            <SelectItem value="oldest">Oldest</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
