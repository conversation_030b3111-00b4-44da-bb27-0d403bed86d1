import { AppDispatch } from "@/store";
import { fileUploadToS3 } from "@/store/slices/commonSlice";
import { Loader2, X } from "lucide-react";
import { useState } from "react";
import { MdCloudUpload } from "react-icons/md";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import { Button } from "../ui/button";
import { Label } from "../ui/label";

interface IProps {
    value: string | null;
    className?: string;
    name: string
    handleAdd: (url: string | null) => void
    handleRemove?: () => void
    disabled?:boolean
}
const VideoUploader = ({ value, handleAdd, className, name, handleRemove, disabled }: IProps) => {
    const dispatch = useDispatch<AppDispatch>()
    const [loading, setLoading] = useState(false)

    const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;

        if (file) {
            if (!file?.type?.startsWith("video/")) {
                toast.warning(`File type "${file.type}" is not accepted.`);
                e.target.value = "";
                return;
            }

            const formData = new FormData();
            formData.append("file", file, file.name);
            setLoading(true)
            try {
                const resultAction = await dispatch(fileUploadToS3(formData));
                const response = resultAction?.payload;

                if (response?.url) {
                    handleAdd?.(response.url);
                } else {
                    toast.error("File upload failed.");
                    handleAdd?.(null);
                }
                setLoading(false)
            } catch (err) {
                console.error("Failed to upload file to S3", err);
                toast.error("Something went wrong while uploading the file.");
                handleAdd?.(null);
                setLoading(false)
            } finally {
                e.target.value = "";
            }
        } else {
            handleAdd?.(null);
        }
    };

    return (
        <>
            {value ?
                <>
                    <div className="bg-slate-200 text-primary  p-5 px-3 rounded-xl flex flex-col gap-3">
                        <div className="relative w-full max-h-[250px]">
                            <Button
                                size={'icon'}
                                variant={'destructive'}
                                onClick={handleRemove}
                                className="absolute top-2 right-2 z-10"
                            >
                                <X />
                            </Button>

                            {value && (
                                <video
                                    src={value}
                                    controls
                                    autoPlay
                                    className={`aspect-auto object-contain max-h-[350px] rounded-lg border w-full`}
                                />
                            )}
                        </div>

                        <span className="text-center font-bold text-sm break-words">
                            {value}
                        </span>
                    </div>
                </>
                :
                <>
                    <Label
                        htmlFor={name}
                        className="w-full h-full cursor-pointer rounded-xl border-2 border-dashed border-gray-300
                                            bg-white hover:bg-slate-50 transition-colors duration-300
                                            flex flex-col items-center justify-center px-4 py-6 text-center group min-h-[140px]"
                    >
                        {loading ? (
                            <div className="flex flex-col items-center justify-center gap-2">
                                <Loader2 className="h-8 w-8 animate-spin text-slate-400" />
                                <span className="text-sm text-gray-400">Uploading...</span>
                            </div>
                        ) : (
                            <>
                                <MdCloudUpload size={35} className="text-blue-800" />
                                <span className="mt-2 text-sm font-medium text-gray-500 group-hover:text-slate-600">
                                    Click to upload video
                                </span>
                            </>
                        )}
                    </Label>
                </>}
            <input
                id={name}
                type='file'
                accept='video/mp4'
                onChange={handleChange}
                className='hidden'
                name={name}
                disabled={disabled}
            />
        </>
    )
}
export default VideoUploader