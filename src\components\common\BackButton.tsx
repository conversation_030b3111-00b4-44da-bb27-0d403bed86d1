'use client'

import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/tooltip"
import { ArrowLeft } from "lucide-react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "../ui/button"

interface IProps {
    icon?: boolean;
    className?: string;
    variant?: "link" | "default" | "destructive" | "outline" | "secondary" | "ghost" | null | undefined
}

const BackButton = ({ icon, className, variant }: IProps) => {
    const router = useRouter()

    const handleBack = () => {
        router.back()
    }

    return (
        <>
            <div>
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            {icon ?
                                <Button className={`self-start text-start border-slate-500 rounded-full ${className}`}
                                    size={'icon'}
                                    variant={variant ?? 'outline'}
                                    onClick={handleBack}>
                                    <ArrowLeft />
                                </Button>
                                : <Button className={`self-start text-start border-slate-500 no-underline ${className}`}
                                    variant={variant ?? 'ghost'}
                                    onClick={handleBack}>
                                    <ArrowLeft />Back
                                </Button>
                            }
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>Back</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
        </>
    )
}
export default BackButton