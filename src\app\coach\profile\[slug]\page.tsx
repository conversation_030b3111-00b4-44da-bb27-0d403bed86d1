import ClientGuard from "@/components/ClientGuard";
import CoachProfile from "@/components/coach/coachProfile/CoachProfile";
import { notFound } from "next/navigation";

const CoachProfilePage = ({ params }: { params: { slug: string } }) => {
    const { slug } = params;
    const validSlugPattern = /^(\d+)-([a-zA-Z]+)-([a-zA-Z]+)$/;
    if (!validSlugPattern.test(slug)) return notFound();    

    return (
        <ClientGuard allowedRoles={[3]}>
            <CoachProfile />
        </ClientGuard>
    )
}
export default CoachProfilePage