'use client'
import { Card, CardContent } from "@/components/ui/card"
import {
    Table,
    TableBody,
    TableCell,
    TableRow
} from "@/components/ui/table"
import { usePaymentData } from "@/contexts/PaymentContext"
import { useConfettiBlast } from "@/hooks/useConfettiBlast"
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"
import { useTokenValues } from "@/hooks/useTokenValues"
import { AppDispatch, RootState } from "@/store"
import { handleUpdateTokenUserValues } from "@/store/slices/auth/loginSlice"
import { fetchBillingStates, fetchPlanSummaryById, handleUpdatePremiumInput, postProcessPayment, validatePromoCode } from "@/store/slices/premiumSlice"
import { ROLES } from "@/utils/constants"
import { EachSearchItem, PaymentData } from "@/utils/interfaces"
import { dollarDecimalAmount } from "@/utils/validations"
import { CheckCircle, CheckCircle2Icon, Loader2, XCircle } from "lucide-react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import BackButton from "../common/BackButton"
import SearchInput from "../common/SearchInput"
import { Alert, AlertDescription, AlertTitle } from "../ui/alert"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import { Skeleton } from "../ui/skeleton"

interface IProps {
    planId: number
}

const Checkout = ({ planId }: IProps) => {
    const { billingStatesList, selectedBillingState, promoCode, planSummaryData, apiStatus } = useSelector((state: RootState) => state.premium)
    const [paymentData, setPaymentData] = useState({
        processingFee: 0,
        serviceFee: 0,
        convenienceFee: 0,
        discountAmount: 0,
        netPlanAmount: 0,
        salesTaxAmount: 0,
        grandTotal: 0,
    });
    const [billingError, setBillingError] = useState('')
    const dispatch = useDispatch<AppDispatch>()
    const router = useRouter()
    const { profileId } = useLocalStoredInfo()
    const { roleId, userId, } = useTokenValues()
    const { setProcessPaymentData } = usePaymentData()
    const fireConfetti = useConfettiBlast()

    const fetchPlanSummary = async () => {
        planId && await dispatch(fetchPlanSummaryById(planId))
    }

    useEffect(() => {
        fetchPlanSummary()
        dispatch(fetchBillingStates())
    }, [dispatch, planId])

    useEffect(() => {
        if (planSummaryData) {
            const discountAmount = planSummaryData?.subscriptionAmount * ((promoCode?.discountPrcnt ?? 0) / 100);

            let netPlanAmount: number;
            let taxAmount: number;
            let serviceingFee: number;
            let processingFee: number;
            let convenienceFee: number;
            let totalFees: number;
            let totalPaidAmount: number;

            if (
                promoCode?.discountPrcnt === 100 ||
                discountAmount === planSummaryData?.subscriptionAmount
            ) {
                // 100% Discount
                netPlanAmount = 0;
                taxAmount = 0;
                processingFee = 0;
                serviceingFee = 0;
                convenienceFee = 0;
                totalFees = 0;
                totalPaidAmount = 0;
            } else {
                //if no discount or discount certain %
                netPlanAmount =
                    discountAmount > 0 ? planSummaryData?.subscriptionAmount - discountAmount : 0;

                const taxPercentage = Number(((selectedBillingState?.tax ?? 0) / 100)?.toFixed(3))
                taxAmount =
                    selectedBillingState?.tax !== 0
                        ? netPlanAmount > 0
                            ? netPlanAmount * taxPercentage
                            : planSummaryData?.subscriptionAmount * taxPercentage
                        : 0;

                processingFee = planSummaryData?.processingFee;
                serviceingFee = planSummaryData?.serviceFee;
                convenienceFee = planSummaryData?.convenienceFee;

                totalFees = processingFee + serviceingFee + convenienceFee

                totalPaidAmount =
                    netPlanAmount > 0
                        ? netPlanAmount + totalFees + taxAmount
                        : planSummaryData?.subscriptionAmount + totalFees + taxAmount
            }

            setPaymentData((prev) => ({
                ...prev,
                processingFee: processingFee || 0,
                serviceFee: serviceingFee || 0,
                convenienceFee: convenienceFee || 0,
                discountAmount: discountAmount || 0,
                netPlanAmount: netPlanAmount || 0,
                salesTaxAmount: taxAmount || 0,
                grandTotal: totalPaidAmount || 0,
            }));
        }
    }, [planSummaryData, selectedBillingState, promoCode, router, planId]);


    const handleOnSelect = (name: string, selected: EachSearchItem | null) => {
        dispatch(handleUpdatePremiumInput({ name, value: selected }))
        setBillingError('')
    }

    const handleUpdatePromoCode = (name: string, value: string) => {
        dispatch(handleUpdatePremiumInput({ name: 'promoCode', value: { ...promoCode, status: '', [name]: value } }))
    }

    const cancelPromoCode = () => {
        dispatch(handleUpdatePremiumInput({ name: 'promoCode', value: null }))
    };

    const handleVerifyPromocode = async () => {
        if (!promoCode?.code) {
            dispatch(handleUpdatePremiumInput({ name: 'promoCode', value: { ...promoCode, status: "Please enter promo code" } }))
        } else {
            const resultAction = await dispatch(validatePromoCode(promoCode?.code ?? ''))
            if (validatePromoCode.fulfilled.match(resultAction)) {
                fireConfetti('promo-card')
            }
        }
    };

    const postFreeTransactionData = async (paymentInfo) => {
        try {
            const payload = {
                roleId,
                userId,
                profileId,
                subscriptionPlanId: +planId,
                paymentInfo: { ...paymentInfo, nonce: null },
            };

            const resultAction = await dispatch(postProcessPayment(payload))
            if (postProcessPayment.fulfilled.match(resultAction)) {
                if (resultAction?.payload) {
                    const { transaction_id, token, user, profileData, } = resultAction?.payload
                    dispatch(handleUpdateTokenUserValues({ token: token, user: user, profile: profileData }))
                    fireConfetti('free-payment-success-card')
                    localStorage.setItem(
                        "userInfo",
                        JSON.stringify(user)
                    );
                    localStorage.setItem(
                        "profileInfo",
                        JSON.stringify(profileData)
                    );
                    localStorage.setItem("profileId", profileData?.id);
                    localStorage.setItem("token", token);
                    localStorage.setItem("userId", user?.id);
                    localStorage.setItem("roleId", user?.roleId);
                    await router.replace(
                        roleId === ROLES.ATHLETE
                            ? `/athlete/active-premium/${transaction_id}`
                            : `/business/active-premium/${transaction_id}`)
                }
            }
        } catch (error) {
            console.error(error)
        }
    }

    const handleProceedToPayment = async (event) => {
        event.preventDefault();
        if (!selectedBillingState?.stateName) {
            setBillingError("Please select a billing state.");
            return;
        }

        if (promoCode?.code && !promoCode?.isVerified) {
            dispatch(handleUpdatePremiumInput({
                name: 'promoCode',
                value: { ...promoCode, status: "Please verify entered promo code or clear it", }
            }))
            return;
        }

        if (promoCode?.status === "Verifying...") {
            event.preventDefault();
            return;
        }

        const paymentInfo = {
            billingStateId: selectedBillingState?.id,
            promoCode: promoCode?.code || '',
            discountPercentage: promoCode?.discountPrcnt || 0,
            taxPercentage: selectedBillingState?.tax,
            subscriptionPlanAmount: planSummaryData?.subscriptionAmount,
            discountAmount: paymentData?.discountAmount,
            netPlanAmount: paymentData?.netPlanAmount,
            taxAmount: Number(paymentData?.salesTaxAmount?.toFixed(3)),
            processingFees: paymentData?.processingFee,
            convenienceFees: paymentData?.convenienceFee,
            serviceFees: paymentData?.serviceFee,
            totalAmountPaid: Number(paymentData?.grandTotal?.toFixed(2)),
        };

        if (
            promoCode?.discountPrcnt === 100 ||
            paymentData?.discountAmount === planSummaryData?.subscriptionAmount
        ) {
            //100% promo
            postFreeTransactionData(paymentInfo);
        } else {
            //For Payment
            setProcessPaymentData(paymentInfo as PaymentData);
            router.push(
                roleId === ROLES.ATHLETE
                    ? `/athlete/premium-plan/checkout/${planId}/payment`
                    : `/business/premium-plan/checkout/${planId}/payment`
            );
        }
    };

    const handleRetrayFreePayment = async () => {
        dispatch(handleUpdatePremiumInput({ name: 'apiStatus', value: '' }))
    }

    if (apiStatus === 'planSummaryLoading') {
        return (
            <div className="w-full space-y-5 bg-white rounded-xl overflow-hidden shadow-md">
                <div className="space-y-2 p-4">
                    <Skeleton className="h-6 w-full bg-slate-200" />
                    <Skeleton className="h-6 w-full bg-slate-200" />
                </div>
                <div className="space-y-2 p-4">
                    {[...Array(5)].map((_, i) => (
                        <Skeleton className="h-6 w-full bg-slate-200" />))}
                </div>
            </div>
        )
    }

    if (apiStatus === 'planSummaryFailed') {
        return (
            <div className="flex flex-col items-center justify-center gap-4 py-10 text-center text-muted-foreground">
                <div className="text-5xl">⚠️</div>
                <h3 className="text-xl font-semibold">We couldn’t load plan summary</h3>
                <p className="max-w-md text-sm">
                    Something went wrong while fetching plan summary. Please check your connection and try again.
                </p>
                <Button
                    onClick={fetchPlanSummary}
                    className="mt-4 hover:bg-primary/90"
                >
                    Try Again
                </Button>
            </div>
        )
    }

    return (
        <>
            <div className="flex flex-col gap-4">
                <div className="grid grid-cols-1 gap-6">
                    <Card className="w-full flex flex-col">
                        {apiStatus === 'postPaymentLoading' ?
                            <div className="flex flex-col items-center gap-4 text-center">
                                <div className="flex items-center justify-center">
                                    <Loader2 className="animate-spin text-primary w-12 h-12" />
                                </div>
                                <h3 className="text-primary text-xl font-semibold">
                                    Processing Your Payment
                                </h3>
                                <p className="text-gray-600 max-w-sm">
                                    Please do not refresh or navigate away while we confirm your payment.
                                    This may take a few seconds...
                                </p>
                            </div>
                            : apiStatus === 'postPaymentFailed' ? (
                                <div className="flex flex-col items-center gap-4 text-center">
                                    <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
                                        <XCircle className="text-red-600 w-8 h-8" />
                                    </div>
                                    <div className="flex items-center gap-1 text-red-600 text-2xl font-semibold">
                                        Payment Unsuccessful
                                    </div>
                                    <p className="text-gray-700">
                                        Unfortunately, We couldn’t process your payment.
                                        You can re-enter your card details and try again —
                                        or choose not to continue.
                                    </p>
                                    <Button onClick={handleRetrayFreePayment}>
                                        Retry
                                    </Button>
                                </div>
                            ) : apiStatus === 'postPaymentSuccess' ? (
                                <div id="free-payment-success-card" className="flex flex-col items-center gap-4 text-center">
                                    <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center">
                                        <CheckCircle className="text-green-600 w-10 h-10" />
                                    </div>
                                    <div className="text-green-600 text-3xl font-semibold">Payment Successful!</div>
                                    <p className="text-gray-700">
                                        Thank you for your payment.
                                        Your transaction has been processed successfully.
                                    </p>
                                </div>
                            ) : (
                                <CardContent className="flex-1 flex flex-col gap-10">
                                    {/* Billing state and promo inputs */}
                                    <div className="flex flex-col gap-5">
                                        <div className="flex flex-col gap-1">
                                            <SearchInput
                                                list={billingStatesList?.map(each => ({ ...each, label: each?.stateName, value: each?.id }))}
                                                name="selectedBillingState"
                                                onChange={(name, selected) => handleOnSelect('selectedBillingState', selected)}
                                                className="w-full"
                                                placeholder="Select billing state..."
                                                value={selectedBillingState as EachSearchItem}
                                            />
                                            {billingError && <span className="text-red-600 text-sm">{billingError}</span>}
                                        </div>
                                        <div className="flex flex-col">
                                            <div className="flex gap-3">
                                                <Input
                                                    type="text"
                                                    placeholder="Enter Promo Code"
                                                    value={promoCode?.code ?? ''}
                                                    onChange={(event) =>
                                                        handleUpdatePromoCode('code', event.target.value)
                                                    }
                                                    onKeyDown={(event) => {
                                                        if (event.key === "Enter") {
                                                            event.preventDefault();
                                                        }
                                                    }}
                                                    disabled={promoCode?.isVerified}
                                                />
                                                {!promoCode?.isVerified ? (
                                                    <Button
                                                        type="button"
                                                        onClick={handleVerifyPromocode}
                                                        disabled={promoCode?.status?.includes('Verifying...')}
                                                    >
                                                        {promoCode?.status?.includes('Verifying...') ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Verify'}
                                                    </Button>
                                                ) : (
                                                    <Button
                                                        type="button"
                                                        variant={'destructive'}
                                                        onClick={cancelPromoCode}
                                                    >
                                                        Cancel
                                                    </Button>
                                                )}
                                            </div>

                                            {promoCode?.status
                                                ?.toLocaleLowerCase()
                                                ?.includes("is valid") ? (
                                                <Alert id="promo-card" className="mt-6 border border-green-300 bg-green-50">
                                                    <CheckCircle2Icon color="#15803d" className="text-green-700" />
                                                    <AlertTitle className="text-green-700">
                                                        {promoCode?.status}
                                                    </AlertTitle>
                                                    <AlertDescription className="text-green-700">
                                                        🎉 Great news! You've unlocked a {promoCode?.discountPrcnt}% discount on your plan.
                                                        Your total has been updated. please click proceed.
                                                    </AlertDescription>
                                                </Alert>

                                            ) : (
                                                <span className={promoCode?.status?.includes('Verifying...')
                                                    ? `text-gray-400` : `text-red-600`}>
                                                    {promoCode?.status}
                                                </span>
                                            )}
                                        </div>
                                    </div>

                                    {/* Loading and plan details */}
                                    <div className="flex flex-col gap-6">
                                        <div className="overflow-x-auto rounded-xl border shadow-md hover:shadow-xl transition-shadow duration-300 border-gray-200">
                                            <Table className="min-w-[400px]">
                                                <TableBody className="shadow-md px-4">
                                                    <TableRow>
                                                        <TableCell className="w-1/2 text-primary px-5">
                                                            Subscription Amount
                                                        </TableCell>
                                                        <TableCell className="w-1/2 text-right font-medium px-5">
                                                            {dollarDecimalAmount(planSummaryData?.subscriptionAmount, 2)}
                                                        </TableCell>
                                                    </TableRow>

                                                    {paymentData?.discountAmount !== 0 && (
                                                        <TableRow>
                                                            <TableCell className="text-primary px-5">
                                                                Discount Amount
                                                            </TableCell>
                                                            <TableCell className="text-right text-red-600 font-medium px-5">
                                                                - {dollarDecimalAmount(paymentData?.discountAmount, 2)}
                                                            </TableCell>
                                                        </TableRow>
                                                    )}

                                                    {paymentData?.netPlanAmount > 0 && (
                                                        <TableRow>
                                                            <TableCell className="text-primary px-5">
                                                                Net Plan Amount
                                                            </TableCell>
                                                            <TableCell className="text-right font-medium px-5">
                                                                {dollarDecimalAmount(paymentData?.netPlanAmount, 2)}
                                                            </TableCell>
                                                        </TableRow>
                                                    )}

                                                    {paymentData?.processingFee > 0 && (
                                                        <TableRow>
                                                            <TableCell className="text-primary px-5">
                                                                Processing Fee
                                                            </TableCell>
                                                            <TableCell className="text-right font-medium px-5">
                                                                {dollarDecimalAmount(paymentData?.processingFee, 2)}
                                                            </TableCell>
                                                        </TableRow>
                                                    )}

                                                    {paymentData?.serviceFee > 0 && (
                                                        <TableRow>
                                                            <TableCell className="text-primary px-5">Service Fee</TableCell>
                                                            <TableCell className="text-right font-medium px-5">
                                                                {dollarDecimalAmount(paymentData?.serviceFee, 2)}
                                                            </TableCell>
                                                        </TableRow>
                                                    )}

                                                    {paymentData?.convenienceFee > 0 && (
                                                        <TableRow>
                                                            <TableCell className="text-primary px-5">
                                                                Convenience Fee
                                                            </TableCell>
                                                            <TableCell className="text-right font-medium px-5">
                                                                {dollarDecimalAmount(paymentData?.convenienceFee, 2)}
                                                            </TableCell>
                                                        </TableRow>
                                                    )}

                                                    {paymentData?.salesTaxAmount > 0 && (
                                                        <TableRow>
                                                            <TableCell className="text-primary px-5">Sales Tax Amount</TableCell>
                                                            <TableCell className="text-right font-medium px-5">
                                                                {dollarDecimalAmount(paymentData?.salesTaxAmount, 3)}
                                                            </TableCell>
                                                        </TableRow>
                                                    )}

                                                    <TableRow>
                                                        <TableCell className="font-bold text-green-600 text-lg px-5">
                                                            Grand Total
                                                        </TableCell>
                                                        <TableCell className="text-right text-lg font-bold text-green-600 px-5">
                                                            {dollarDecimalAmount(paymentData?.grandTotal, 2)}
                                                        </TableCell>
                                                    </TableRow>
                                                </TableBody>
                                            </Table>
                                        </div>

                                        <div className="flex items-center justify-between w-full">
                                            <BackButton variant={'outline'} icon={false} className="border border-slate-300 text-primary no-underline" />
                                            <Button onClick={handleProceedToPayment} className="min-w-24">
                                                {apiStatus === 'postPaymentLoading' ? <Loader2 className="animate-spin w-32" />
                                                    : promoCode?.discountPrcnt === 100 ? 'Proceed' : "Proceed to payment"}
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            )}
                    </Card>
                </div>
            </div>
        </>
    )
}
export default Checkout