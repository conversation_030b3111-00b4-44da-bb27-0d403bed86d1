import { NextRequest, NextResponse } from "next/server";
import axios from "axios";

export async function POST(req: NextRequest) {
    try {
        const body = await req.json();
        const {
            email,
            password,
            confirmPassword,
            otp,
            role,
            formData,
        } = body;

        if (!email || !role) {
            return NextResponse.json({ error: "Email and role are required." }, { status: 400 });
        }

        // 🔹 STEP 1: Send OTP if no password/otp submitted
        const isStep1 = !password && !otp && !confirmPassword;
        if (isStep1) {
            const res = await axios.post(`${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/register`, {
                email,
                role,
                ...formData,
            });

            return NextResponse.json({
                message: "OTP sent successfully.",
                data: res.data,
            });
        }

        // 🔸 STEP 2: Complete registration with OTP and password
        if (!otp || !password || !confirmPassword) {
            return NextResponse.json(
                { error: "OTP, password, and confirmPassword are required." },
                { status: 400 }
            );
        }

        if (password !== confirmPassword) {
            return NextResponse.json({ error: "Passwords do not match" }, { status: 400 });
        }

        const res = await axios.post(`${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/register`, {
            email,
            password,
            otp,
            role,
            ...formData,
        });

        return NextResponse.json({
            message: "Registered successfully",
            data: res.data,
        });
    } catch (error: any) {
        const status = error.response?.status || 500;
        const message = error.response?.data?.message || "Registration failed";
        return NextResponse.json({ error: message }, { status });
    }
}
