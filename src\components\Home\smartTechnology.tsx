"use client";
import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { Zap } from "lucide-react";
import parse, { domToReact } from "html-react-parser";
import {
  titleParseOptions,
  shortDescriptionParseOptions,
  fileTitleParseOptions,
  fileDescriptionParseOptions,
} from "@/utils/parseOptions";
import { Button } from "../ui/button";

const TechnologySection = () => {
  const { smartTechnologySection, smartTechnologyImages, loading } =
    useSelector((state: RootState) => state.homeCMS);

  if (loading || !smartTechnologySection) return null;

    const handleNavClick = (e: any, route: string) => {
    e.preventDefault();
    const el = document.querySelector(route);
    if (el) {
      el.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  return (
    <section className="py-20 px-3 sm:px-8 md:px-16 lg:px-36 xl:px-56 bg-[#0D1D3A] relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 z-0 opacity-10 pointer-events-none">
        {/* Blur Pulses */}
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-blue-400 to-purple-600 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-br from-purple-400 to-pink-600 rounded-full blur-3xl animate-pulse delay-500"></div>

        {/* Abstract Orange Shape */}
        <div className="absolute top-[-50px] right-[-50px] w-[400px] h-[400px] bg-orange-400 rounded-full blur-[150px] opacity-30 z-0"></div>

        {/* Orange diagonal lines */}
        <svg
          className="absolute top-0 right-0 h-full w-[400px] z-0"
          viewBox="0 0 100 800"
          preserveAspectRatio="none"
        >
          <defs>
            <linearGradient id="grad" x1="0" x2="1" y1="0" y2="1">
              <stop offset="0%" stopColor="#f97316" />
              <stop offset="100%" stopColor="#f43f5e" />
            </linearGradient>
          </defs>
          <line
            x1="10"
            y1="0"
            x2="90"
            y2="800"
            stroke="url(#grad)"
            strokeWidth="2"
          />
          <line
            x1="30"
            y1="0"
            x2="110"
            y2="800"
            stroke="url(#grad)"
            strokeWidth="2"
          />
          <line
            x1="50"
            y1="0"
            x2="130"
            y2="800"
            stroke="url(#grad)"
            strokeWidth="2"
          />
        </svg>

        {/* Bottom Wave */}
        <svg
          className="absolute bottom-0 left-0 w-full opacity-10"
          viewBox="0 0 1440 320"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill="#ffffff"
            fillOpacity="0.1"
            d="M0,64L80,85.3C160,107,320,149,480,165.3C640,181,800,171,960,149.3C1120,128,1280,96,1360,80L1440,64L1440,320L1360,320C1280,320,1120,320,960,320C800,320,640,320,480,320C320,320,160,320,80,320L0,320Z"
          />
        </svg>
      </div>

      <div className="container mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          {/* SPARQX Banner – unchanged */}
          <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-orange-900 to-orange-500 px-4 py-2 rounded-full mb-6">
            <Zap className="w-5 h-5 text-blue-400" />
            <span className="text-sm font-semibold text-white">
              Powered by SPARQX™ Engine
            </span>
          </div>

          <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-6">
            {parse(smartTechnologySection.title || "", titleParseOptions)}
          </h2>

          <p className="cms-text text-lg text-gray-300 leading-relaxed max-w-3xl mx-auto">
            {parse(
              smartTechnologySection.description || "",
              shortDescriptionParseOptions
            )}
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {smartTechnologyImages.map((feature, index) => (
            <div
              key={feature.id}
              className="group relative bg-white/5 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-2xl border border-white/10 transition-all duration-500 hover:-translate-y-2"
              style={{
                animationDelay: `${index * 150}ms`,
                animation: "slideInUp 0.8s ease-out forwards",
              }}
            >
              {/* Hover Gradient */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-500"></div>

              <div className="relative z-10 text-center space-y-4">
                {/* <div className="inline-flex w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform duration-300 mb-4 text-lg font-bold">
                  {feature.fileTitle?.charAt(0) || "A"}
                </div> */}
                <img
                  src={feature?.fileLocation}
                  alt={feature?.fileTitle}
                  className="w-16 h-16 rounded-full mx-auto mb-4 object-cover"
                />
                <h3 className="text-xl font-bold text-white group-hover:text-blue-400 transition-colors duration-300">
                  {parse(feature.fileTitle || "", fileTitleParseOptions)}
                </h3>
                <p className="cms-text text-gray-300 leading-relaxed">
                  {parse(
                    feature.fileDescription || "",
                    fileDescriptionParseOptions
                  )}
                </p>
              </div>

              {/* Dots */}
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-blue-400/30 to-purple-600/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-gradient-to-br from-green-400/30 to-blue-600/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500 delay-100"></div>
            </div>
          ))}
        </div>

        {/* CTA */}
        {/* <div className="text-center">
          <Button onClick={(e) => handleNavClick(e, '#registration')} variant={"primaryGradient"} className="hover:shadow-2xl">
            Sign up For Free
          </Button>
        </div> */}
      </div>

      {/* Animation Keyframes */}
      <style>{`
        @keyframes slideInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </section>
  );
};

export default TechnologySection;
