"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";

const TermsPage = () => {
  const { userType } = useParams();
  const [termsData, setTermsData] = useState<{
    title: string;
    content: string;
  } | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!userType) return;

    const fetchTerms = async () => {
      try {
        const res = await fetch(`/api/get-terms?type=${userType}`);
        const data = await res.json();
        if (data.status === 200) {
          setTermsData(data.data);
        }
      } catch (err) {
        console.error("Failed to load terms:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchTerms();
  }, [userType]);

  if (loading)
    return (
      <div className="p-6 text-gray-700">Loading Terms & Conditions...</div>
    );

  if (!termsData)
    return (
      <div className="p-6 text-red-500">Unable to load Terms & Conditions.</div>
    );

  return (
    <div className="max-w-4xl mx-auto p-6 mt-16">
      <div
        className="prose prose-sm md:prose lg:prose-lg max-w-none"
        dangerouslySetInnerHTML={{
          __html: termsData.title + termsData.content,
        }}
      />
    </div>
  );
};

export default TermsPage;
