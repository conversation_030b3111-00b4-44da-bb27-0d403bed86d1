'use client'
import CommonCalender from "@/components/common/CommonCalender"
import SearchInput from "@/components/common/SearchInput"
import TimeSlotPicker from "@/components/common/TimeSlotPicker"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"

import { Textarea } from "@/components/ui/textarea"
import { AppDispatch, RootState } from "@/store"
import { deleteCoachAvailability, editCoachAvailability, fetchCoachAvailability, getCoachTimeslots, getCoachTimeZonesType, handleCoachInputChange, postCoachAvailability } from "@/store/slices/coach/coachProfileSlice"
import { useTokenValues } from "@/hooks/useTokenValues"
import { AvailabilitySlot, EachSearchItem } from "@/utils/interfaces"
import { Edit, Loader, Plus, Trash2 } from "lucide-react"
import { ChangeEvent, useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"

const GeneralAvailability = () => {
    const { toggleAvailability, allTimeZoneList, allTimeSlotsList, availableTimeZone, generalAvailabilityList, toBookTime,
        toggleToBookTime, availabilityNote, toggleAvailabilityNote, loading, coachProfileData } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch<AppDispatch>()
    const { userId } = useTokenValues()
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [editingSlot, setEditingSlot] = useState<number | null>(null)
    const [editFormData, setEditFormData] = useState({
        fromTimeSlotsId: null as number | null,
        toTimeSlotsId: null as number | null,
        webLink: "",
        notes: "",
    })

    // Fetch time zones, time slots, and availability data on component mount
    useEffect(() => {
        dispatch(getCoachTimeZonesType())
        dispatch(getCoachTimeslots())
        dispatch(fetchCoachAvailability())
    }, [dispatch])

    const handleToggleSection = (name: string, checked: boolean) => {
        dispatch(handleCoachInputChange({ name, value: checked }))
    }

    const handleSelectChange = (name: string, selected: EachSearchItem | null) => {
        dispatch(handleCoachInputChange({ name, value: selected }))
    }

    const handleAvailabilityCheckbox = (dayId: string, isAvailable: boolean | string) => {
        const updatedList = generalAvailabilityList.map((item) =>
            item.id === dayId ? { ...item, isAvailable } : item
        );

        dispatch(
            handleCoachInputChange({
                name: 'generalAvailabilityList',
                value: updatedList,
            })
        );
    };




    const handleDateSelect = (date: Date | undefined) => {
        dispatch(handleCoachInputChange({ name: 'toBookTime', value: date }))
    }

    const handleOnChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
        const { name, value } = event.target
        dispatch(handleCoachInputChange({ name, value }))
    }

    const handleAddSlot = (dayId: string) => {
        const newSlot: AvailabilitySlot = {
            id: `${dayId}-${Date.now()}`,
            fromTimeSlotsId: null,
            toTimeSlotsId: null,
            webLink: "",
            notes: "",
            isHidden: false,
        }

        const updatedList = generalAvailabilityList.map((item) =>
            item.id === dayId ? { ...item, slots: [...item.slots, newSlot] } : item
        );

        dispatch(handleCoachInputChange({ name: 'generalAvailabilityList', value: updatedList }));
    }

    const handleRemoveSlot = (dayId: string, slotId: string) => {
        const updatedList = generalAvailabilityList.map((item) =>
            item.id === dayId
                ? { ...item, slots: item.slots.filter(slot => slot.id !== slotId) }
                : item
        );

        dispatch(handleCoachInputChange({ name: 'generalAvailabilityList', value: updatedList }));
    }

    const handleSlotChange = (dayId: string, slotId: string, field: keyof AvailabilitySlot, value: any) => {
        const updatedList = generalAvailabilityList.map((item) =>
            item.id === dayId
                ? {
                    ...item,
                    slots: item.slots.map(slot =>
                        slot.id === slotId ? { ...slot, [field]: value } : slot
                    )
                }
                : item
        );

        dispatch(handleCoachInputChange({ name: 'generalAvailabilityList', value: updatedList }));
    }

    const handleSaveAvailability = async () => {
        setIsSubmitting(true)
        try {
            const payload: any[] = []

            generalAvailabilityList.forEach(day => {
                if (day.isAvailable && day.slots.length > 0) {
                    day.slots.forEach(slot => {
                        if (slot.fromTimeSlotsId && slot.toTimeSlotsId) {
                            payload.push({
                                roleId: 3,
                                coachId: coachProfileData?.id || Number(userId || 0),
                                userId: Number(userId || 0),
                                usTimezonesId: availableTimeZone?.value || 3,
                                dayVal: day.day,
                                isAvailable: true,
                                fromTimeSlotsId: slot.fromTimeSlotsId,
                                toTimeSlotsId: slot.toTimeSlotsId,
                                webLink: slot.webLink || null,
                                notes: slot.notes || "",
                                isHidden: slot.isHidden,
                            })
                        }
                    })
                }
            })

            if (payload.length > 0) {
                const resultAction = await dispatch(postCoachAvailability(payload))
                if (postCoachAvailability.fulfilled.match(resultAction)) {
                    // Refresh the availability data after successful save
                    await dispatch(fetchCoachAvailability())
                    // Clear the new slots after saving
                    const updatedList = generalAvailabilityList.map(day => ({
                        ...day,
                        slots: []
                    }))
                    dispatch(handleCoachInputChange({ name: 'generalAvailabilityList', value: updatedList }))
                }
            }
        } catch (error) {
            console.error("Error saving availability:", error)
        } finally {
            setIsSubmitting(false)
        }
    }

    const handleEditSlot = (apiSlot: any) => {
        setEditingSlot(apiSlot.id)
        setEditFormData({
            fromTimeSlotsId: apiSlot.fromTimeSlotsId,
            toTimeSlotsId: apiSlot.toTimeSlotsId,
            webLink: apiSlot.webLink || "",
            notes: apiSlot.notes || "",
        })
    }

    const handleCancelEdit = () => {
        setEditingSlot(null)
        setEditFormData({
            fromTimeSlotsId: null,
            toTimeSlotsId: null,
            webLink: "",
            notes: "",
        })
    }

    const handleSaveEdit = async (slotId: number) => {
        setIsSubmitting(true)
        try {
            const payload = {
                id: slotId,
                roleId: 3,
                coachId: coachProfileData?.id || Number(userId || 0),
                userId: Number(userId || 0),
                usTimezonesId: availableTimeZone?.value || 3,
                fromTimeSlotsId: editFormData.fromTimeSlotsId,
                toTimeSlotsId: editFormData.toTimeSlotsId,
                webLink: editFormData.webLink || null,
                notes: editFormData.notes || "",
                isHidden: false,
            }

            const resultAction = await dispatch(editCoachAvailability(payload))
            if (editCoachAvailability.fulfilled.match(resultAction)) {
                await dispatch(fetchCoachAvailability())
                handleCancelEdit()
            }
        } catch (error) {
            console.error("Error updating availability:", error)
        } finally {
            setIsSubmitting(false)
        }
    }

    const handleDeleteSlot = async (slotId: number) => {
        if (window.confirm("Are you sure you want to delete this availability slot?")) {
            setIsSubmitting(true)
            try {
                const resultAction = await dispatch(deleteCoachAvailability(slotId))
                if (deleteCoachAvailability.fulfilled.match(resultAction)) {
                    await dispatch(fetchCoachAvailability())
                }
            } catch (error) {
                console.error("Error deleting availability:", error)
            } finally {
                setIsSubmitting(false)
            }
        }
    }

    const handleEditFormChange = (field: string, value: any) => {
        setEditFormData(prev => ({
            ...prev,
            [field]: value
        }))
    }

    return (
        <>
            <div className="w-full h-full flex flex-col items-center gap-3 bg-slate-100 p-4 rounded-lg">
                <div className="flex items-center justify-center gap-4">
                    <h3 className="font-bold text-xl text-center">General Availability</h3>
                    <Switch
                        name='toggleAvailability'
                        checked={toggleAvailability}
                        onCheckedChange={(checked) => handleToggleSection('toggleAvailability', checked)}
                    />
                </div>

                {toggleAvailability ?
                    <div className="flex flex-col gap-8 w-full">
                        <div className="flex flex-col gap-2">
                            <Label>Time Zone</Label>
                            <SearchInput
                                list={allTimeZoneList}
                                name='availableTimeZone'
                                onChange={handleSelectChange}
                                placeholder="Select Time Zone..."
                            />
                        </div>

                        <div className="space-y-6">
                            {generalAvailabilityList?.map(day => (
                                <div key={day.id} className="border rounded-lg p-4 bg-white">
                                    <div className="flex items-center justify-between mb-4">
                                        <div className="flex items-center gap-3">
                                            <h4 className="font-semibold text-lg">{day.day}</h4>
                                            <Checkbox
                                                className="border-slate-500 h-4 w-4"
                                                checked={day.isAvailable}
                                                onCheckedChange={(checked) => handleAvailabilityCheckbox(day.id, checked)}
                                            />
                                            <span className="text-sm text-slate-600">Available</span>
                                        </div>
                                        {day.isAvailable && (
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleAddSlot(day.id)}
                                                className="flex items-center gap-2"
                                            >
                                                <Plus size={16} />
                                                Add Slot
                                            </Button>
                                        )}
                                    </div>

                                    {day.isAvailable && (
                                        <div className="space-y-3">
                                            {/* Display existing API slots */}
                                            {day.apiSlots && day.apiSlots.length > 0 && (
                                                <div className="space-y-3">
                                                    <h5 className="text-sm font-medium text-slate-700">Current Availability:</h5>
                                                    {day.apiSlots.map(apiSlot => (
                                                        <div key={apiSlot.id} className="border rounded p-3 bg-blue-50 border-blue-200">
                                                            {editingSlot === apiSlot.id ? (
                                                                // Edit Mode
                                                                <div className="space-y-4">
                                                                    <div className="flex justify-between items-center">
                                                                        <h6 className="font-medium text-slate-700">Edit Availability Slot</h6>
                                                                        <div className="flex gap-2">
                                                                            <Button
                                                                                variant="outline"
                                                                                size="sm"
                                                                                onClick={() => handleSaveEdit(apiSlot.id)}
                                                                                disabled={isSubmitting}
                                                                                className="text-green-600 hover:text-green-700"
                                                                            >
                                                                                {isSubmitting ? <Loader className="h-4 w-4 animate-spin" /> : "Save"}
                                                                            </Button>
                                                                            <Button
                                                                                variant="outline"
                                                                                size="sm"
                                                                                onClick={handleCancelEdit}
                                                                                disabled={isSubmitting}
                                                                            >
                                                                                Cancel
                                                                            </Button>
                                                                        </div>
                                                                    </div>
                                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                                                        <div>
                                                                            <Label className="text-sm">Start Time</Label>
                                                                            <TimeSlotPicker
                                                                                name="fromTime"
                                                                                timeSlots={allTimeSlotsList}
                                                                                value={editFormData.fromTimeSlotsId}
                                                                                onChange={(_, timeSlotId) => handleEditFormChange('fromTimeSlotsId', timeSlotId)}
                                                                                placeholder="Select start time"
                                                                            />
                                                                        </div>
                                                                        <div>
                                                                            <Label className="text-sm">End Time</Label>
                                                                            <TimeSlotPicker
                                                                                name="toTime"
                                                                                timeSlots={allTimeSlotsList}
                                                                                value={editFormData.toTimeSlotsId}
                                                                                onChange={(_, timeSlotId) => handleEditFormChange('toTimeSlotsId', timeSlotId)}
                                                                                placeholder="Select end time"
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                    <div>
                                                                        <Label className="text-sm">Web Link</Label>
                                                                        <Input
                                                                            placeholder="https://zoom.us/j/123456789"
                                                                            value={editFormData.webLink}
                                                                            onChange={(e) => handleEditFormChange('webLink', e.target.value)}
                                                                        />
                                                                    </div>
                                                                    <div>
                                                                        <Label className="text-sm">Notes</Label>
                                                                        <Input
                                                                            placeholder="Session notes..."
                                                                            value={editFormData.notes}
                                                                            onChange={(e) => handleEditFormChange('notes', e.target.value)}
                                                                        />
                                                                    </div>
                                                                </div>
                                                            ) : (
                                                                // View Mode
                                                                <div>
                                                                    <div className="flex justify-between items-start mb-3">
                                                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 flex-1">
                                                                            <div>
                                                                                <Label className="text-sm font-medium">Time Slot</Label>
                                                                                <p className="text-sm text-slate-700">
                                                                                    {apiSlot.fromTimeSlot.time12hr} - {apiSlot.toTimeSlot.time12hr}
                                                                                </p>
                                                                            </div>
                                                                            <div>
                                                                                <Label className="text-sm font-medium">Timezone</Label>
                                                                                <p className="text-sm text-slate-700">
                                                                                    {apiSlot.usTimezones.timezoneName} ({apiSlot.usTimezones.abbreviation})
                                                                                </p>
                                                                            </div>
                                                                            <div>
                                                                                <Label className="text-sm font-medium">Status</Label>
                                                                                <p className="text-sm text-green-600 font-medium">
                                                                                    {apiSlot.isAvailable ? 'Available' : 'Not Available'}
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                        <div className="flex gap-2 ml-4">
                                                                            <Button
                                                                                variant="outline"
                                                                                size="sm"
                                                                                onClick={() => handleEditSlot(apiSlot)}
                                                                                disabled={isSubmitting}
                                                                                className="text-blue-600 hover:text-blue-700"
                                                                            >
                                                                                <Edit size={16} />
                                                                            </Button>
                                                                            <Button
                                                                                variant="outline"
                                                                                size="sm"
                                                                                onClick={() => handleDeleteSlot(apiSlot.id)}
                                                                                disabled={isSubmitting}
                                                                                className="text-red-600 hover:text-red-700"
                                                                            >
                                                                                <Trash2 size={16} />
                                                                            </Button>
                                                                        </div>
                                                                    </div>
                                                                    {(apiSlot.webLink || apiSlot.notes) && (
                                                                        <div className="mt-3 pt-3 border-t border-blue-200">
                                                                            {apiSlot.webLink && (
                                                                                <div className="mb-2">
                                                                                    <Label className="text-sm font-medium">Web Link</Label>
                                                                                    <p className="text-sm text-blue-600 break-all">
                                                                                        <a href={apiSlot.webLink} target="_blank" rel="noopener noreferrer" className="hover:underline">
                                                                                            {apiSlot.webLink}
                                                                                        </a>
                                                                                    </p>
                                                                                </div>
                                                                            )}
                                                                            {apiSlot.notes && (
                                                                                <div>
                                                                                    <Label className="text-sm font-medium">Notes</Label>
                                                                                    <p className="text-sm text-slate-700">{apiSlot.notes}</p>
                                                                                </div>
                                                                            )}
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                    ))}
                                                </div>
                                            )}

                                            {/* Display new slots being added */}
                                            {day.slots.length > 0 && (
                                                <div className="space-y-3">
                                                    <h5 className="text-sm font-medium text-slate-700">Add New Slots:</h5>
                                                    {day.slots.map(slot => (
                                                        <div key={slot.id} className="border rounded p-3 bg-slate-50">
                                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mb-3">
                                                                <div>
                                                                    <Label className="text-sm">Start Time</Label>
                                                                    <TimeSlotPicker
                                                                        name="fromTime"
                                                                        timeSlots={allTimeSlotsList}
                                                                        value={slot.fromTimeSlotsId}
                                                                        onChange={(_, timeSlotId) => handleSlotChange(day.id, slot.id, 'fromTimeSlotsId', timeSlotId)}
                                                                        placeholder="Select start time"
                                                                    />
                                                                </div>
                                                                <div>
                                                                    <Label className="text-sm">End Time</Label>
                                                                    <TimeSlotPicker
                                                                        name="toTime"
                                                                        timeSlots={allTimeSlotsList}
                                                                        value={slot.toTimeSlotsId}
                                                                        onChange={(_, timeSlotId) => handleSlotChange(day.id, slot.id, 'toTimeSlotsId', timeSlotId)}
                                                                        placeholder="Select end time"
                                                                    />
                                                                </div>
                                                                <div>
                                                                    <Label className="text-sm">Web Link</Label>
                                                                    <Input
                                                                        placeholder="https://zoom.us/j/123456789"
                                                                        value={slot.webLink}
                                                                        onChange={(e) => handleSlotChange(day.id, slot.id, 'webLink', e.target.value)}
                                                                    />
                                                                </div>
                                                                <div className="flex items-end">
                                                                    <Button
                                                                        variant="outline"
                                                                        size="sm"
                                                                        onClick={() => handleRemoveSlot(day.id, slot.id)}
                                                                        className="text-red-600 hover:text-red-700"
                                                                    >
                                                                        <Trash2 size={16} />
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <Label className="text-sm">Notes</Label>
                                                                <Input
                                                                    placeholder="Session notes..."
                                                                    value={slot.notes}
                                                                    onChange={(e) => handleSlotChange(day.id, slot.id, 'notes', e.target.value)}
                                                                />
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>

                        <div className="flex justify-end">
                            <Button
                                onClick={handleSaveAvailability}
                                disabled={isSubmitting || loading}
                                className="px-8"
                            >
                                {isSubmitting || loading ? (
                                    <>
                                        <Loader className="mr-2 h-4 w-4 animate-spin" />
                                        Saving...
                                    </>
                                ) : (
                                    "Save Availability"
                                )}
                            </Button>
                        </div>

                        {/* <div className="flex flex-col gap-3">
                            <div className="flex items-center gap-2">
                                <Label>To Book Time</Label>
                                <Switch
                                    name={'toggleToBookTime'}
                                    checked={toggleToBookTime}
                                    onCheckedChange={(checked) => handleToggleSection('toggleToBookTime', checked)}
                                />
                            </div>
                            {toggleToBookTime && <CommonCalender
                                mode={'single'}
                                dateValue={toBookTime}
                                setDateFn={handleDateSelect}
                            />}
                        </div> */}

                        {/* <div className="flex flex-col gap-3">
                            <div className="flex items-center gap-2">
                                <Label>Note</Label>
                                <Switch
                                    name={'toggleAvailabilityNote'}
                                    checked={toggleAvailabilityNote}
                                    onCheckedChange={(checked) => handleToggleSection('toggleAvailabilityNote', checked)}
                                />
                            </div>
                            {toggleAvailabilityNote &&
                                <>
                                    <Textarea
                                        name='availabilityNote'
                                        className="bg-white"
                                        placeholder="Write Notes..."
                                        value={availabilityNote}
                                        onChange={handleOnChange}
                                        maxLength={400}
                                        onInput={(e) => {
                                            const input = e.currentTarget;
                                            if (input.value.length > 400) {
                                                input.value = input.value.slice(0, 400);
                                            }
                                        }}
                                    />
                                    <div className="flex justify-end">
                                        <span className="text-red-500 text-sm">400 char max</span>
                                    </div>
                                </>
                            }
                        </div> */}
                    </div> : null}
            </div>
        </>
    )
}
export default GeneralAvailability