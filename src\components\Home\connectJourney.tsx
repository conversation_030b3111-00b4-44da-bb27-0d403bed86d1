import React from "react";
import { Instagram, Twitter } from "lucide-react";

const ConnectJourney = () => {
  return (
    <section
      id="connect-journey"
      className="bg-[#0D1D3A] text-white py-12 px-4 sm:px-8 md:px-16 lg:px-36 xl:px-56 w-full"
    >
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-6">
          Follow the Journey. Join the Movement.
        </h2>
        <p className="cms-text text-lg text-gray-300 leading-relaxed max-w-3xl mx-auto">
          Stay updated with stories, updates, and behind-the-scenes moments from
          the Connect Athlete community.
        </p>
      </div>

      <div className="flex flex-col items-center justify-center gap-6 text-lg text-gray-200">
        <div className="flex flex-col sm:flex-row items-center gap-6">
          {/* Instagram Handle */}
          <a
            href="https://www.instagram.com/connectathlete/"
            target="_blank"
            rel="noopener noreferrer"
            className="relative group overflow-hidden px-6 py-3 rounded-full border border-gray-700 hover:shadow-xl transition"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-500 opacity-20 rounded-full blur-xl scale-0 group-hover:scale-100 transition-transform duration-500" />
            <div className="relative flex items-center gap-2 z-10">
              <Instagram className="w-6 h-6 text-white transition-transform group-hover:scale-125 group-hover:animate-pulse duration-300" />
              <span className="font-medium">@ConnectAthlete</span>
            </div>
          </a>

          {/* Twitter (X) Handle */}
          <a
            href="https://x.com/ConnectAthlete_"
            target="_blank"
            rel="noopener noreferrer"
            className="relative group overflow-hidden px-6 py-3 rounded-full border border-gray-700 hover:shadow-xl transition"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-sky-400 opacity-20 rounded-full blur-xl scale-0 group-hover:scale-100 transition-transform duration-500" />
            <div className="relative flex items-center gap-2 z-10">
              <img
                src="/landing-page-images/twitter-x.png"
                alt="Logo"
                className="w-6 h-6 text-white transition-transform group-hover:scale-125 group-hover:animate-pulse duration-300"
              />
              <span className="font-medium">@ConnectAthlete_</span>
            </div>
          </a>
        </div>

        <div className="text-sm text-blue-300 mt-4">
          Tag us in your journey →{" "}
          <span className="font-medium text-white">#ConnectAthlete</span>
        </div>
      </div>
    </section>
  );
};

export default ConnectJourney;
