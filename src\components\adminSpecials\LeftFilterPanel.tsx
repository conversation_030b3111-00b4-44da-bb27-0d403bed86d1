'use client';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import BackButton from "../common/BackButton";

const LeftFilterPanel = () => {
  return (
    <div className="w-full md:w-72 rounded-2xl p-4 space-y-4 bg-white shadow">
      <div className="flex items-center justify-between">
        <BackButton />
        <div className="w-10 h-10 border-2 border-blue-700 rounded-full flex items-center justify-center">
          <span role="img" aria-label="calendar" className="text-xl">📅</span>
        </div>
      </div>

      {[
        { label: 'Type' },
        { label: 'Category' },
        { label: 'Coach /Business(/Org)' },
        { label: 'Profile Name' },
        { label: 'Business Type (only for Business /org)' },
        { label: 'Channel' },
        { label: 'State' },
        { label: 'Location' },
        { label: 'Sport' },
        { label: 'Speciality/Position' },
      ].map(({ label }, idx) => (
        <div key={idx} className="flex flex-col">
          <label className="text-sm font-semibold text-blue-900 mb-1">{label}</label>
          <Select>
            <SelectTrigger className="border border-gray-300 rounded-md p-2 text-sm bg-white focus:outline-none focus:ring focus:ring-blue-200">
              <SelectValue placeholder="Select..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="relevance">Relevance</SelectItem>
              <SelectItem value="latest">Latest</SelectItem>
              <SelectItem value="oldest">Oldest</SelectItem>
            </SelectContent>
          </Select>
        </div>
      ))}
    </div>
  );
};

export default LeftFilterPanel;
