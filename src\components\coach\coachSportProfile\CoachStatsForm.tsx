import CommonCalender from "@/components/common/CommonCalender"
import NumericInput from "@/components/common/NumericInput"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from "@/components/ui/table"
import { Textarea } from "@/components/ui/textarea"
import { RootState } from "@/store"
import { emptyAthletePlacementsList, emptyStatsFormData, handleCoachSportInputChange, updateAthletePlacement } from "@/store/slices/coach/coachSportSlice"
import { useDispatch, useSelector } from "react-redux"

const CoachStatsForm = () => {
    const { toggleStatsForm, statsFormData, athletePlacementsList, addedStatsScoreCardList } = useSelector((state: RootState) => state.coachSport)
    const dispatch = useDispatch()

    const handleOnChange = (name: string, value: boolean) => {
        dispatch(handleCoachSportInputChange({ name, value }))
    }

    const handleChangeStatsForm = (name: string, value: Date | undefined | string | boolean | number | null) => {
        dispatch(handleCoachSportInputChange({ name: 'statsFormData', value: { ...statsFormData, [name]: value } }))
    }

    const handleSaveStatsForm = () => {
        if (statsFormData?.statsAsOf) {
            const filteredPlacements = athletePlacementsList.filter(
                (placement) => placement.athletesTrained !== "" && placement.athletesTrained !== null && placement.athletesTrained !== undefined
            );

            const payload = {
                id: `${Date.now()}-${Math.floor(Math.random() * 10000)}`,
                ...statsFormData,
                athletePlacements: filteredPlacements
            }
            dispatch(handleCoachSportInputChange({ name: 'statsFormData', value: emptyStatsFormData }))
            dispatch(handleCoachSportInputChange({ name: 'athletePlacementsList', value: emptyAthletePlacementsList }))
            dispatch(handleCoachSportInputChange({ name: 'addedStatsScoreCardList', value: [...addedStatsScoreCardList, payload] }))
            const scoreCardComponent = document.getElementById('statsScoreCard')
            scoreCardComponent?.scrollIntoView({ behavior: 'smooth' });
        }
    }


    return (
        <>
            <div className="bg-slate-100 p-5 flex flex-col gap-5 rounded-lg">
                <div>
                    <div className="flex items-center justify-center gap-5">
                        <h3 className="text-xl font-bold">Coach Stats Entry Form</h3>
                        <Switch
                            checked={toggleStatsForm}
                            onCheckedChange={(checked) => handleOnChange('toggleStatsForm', checked)}
                        />
                    </div>
                    <p className="text-md text-center">These metrics are self-reported by the coach and are not yet verified by Connect Athlete</p>
                </div>

                {toggleStatsForm ?
                    <>
                        {/* <div className="flex items-end justify-end">
                            <Button>
                                <Plus /> Add
                            </Button>
                        </div> */}

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                            <div className="">
                                <Label>Stats As Of</Label>
                                <CommonCalender
                                    mode="single"
                                    placeholder="Pick a date"
                                    dateValue={statsFormData?.statsAsOf}
                                    setDateFn={(date) => handleChangeStatsForm('statsAsOf', date)}
                                />
                            </div>

                            <div className="flex flex-col gap-2">
                                <Label>Years Coaching</Label>
                                <NumericInput
                                    placeholder="No.of Years Coaching"
                                    value={statsFormData?.yearsCoaching}
                                    onValueChange={(value) => handleChangeStatsForm('yearsCoaching', value)}
                                />
                            </div>

                            <div className="flex flex-col items-center justify-center gap-2 col-span-full">
                                <div className="flex items-center gap-3">
                                    <Label>Athletes Trained</Label>
                                    <Switch
                                        checked={statsFormData?.toggleAthletesTrained}
                                        onCheckedChange={(checked) => handleChangeStatsForm('toggleAthletesTrained', checked)}
                                    />
                                </div>
                                <NumericInput
                                    placeholder="No.of Athletes Trained"
                                    value={statsFormData?.athletesTrained}
                                    className="text-center w-full md:w-[50%]"
                                    onValueChange={(value) => handleChangeStatsForm('athletesTrained', value)}
                                />
                            </div>

                            <div className="col-span-full my-8 rounded-lg border border-slate-300 overflow-hidden">
                                <Table className="w-full">
                                    <TableHeader>
                                        <TableRow className="text-lg border-b border-slate-300">
                                            <TableHead className="font-bold">Placement Breakdown</TableHead>
                                            <TableHead className="font-bold text-center">Athletes Trained</TableHead>
                                            <TableHead className="font-bold text-center">Hide/Unhide</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {athletePlacementsList?.map((each) => (
                                            <TableRow key={each?.id} className="border-b border-slate-300 last:border-b-0">
                                                <TableCell className="text-wrap">
                                                    {each?.placement}
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    <NumericInput
                                                        placeholder="Enter Sum of..."
                                                        value={each?.athletesTrained}
                                                        onValueChange={(value) =>
                                                            dispatch(updateAthletePlacement({
                                                                id: each.id,
                                                                key: "athletesTrained",
                                                                value,
                                                            }))
                                                        }
                                                    />
                                                </TableCell>
                                                <TableCell className="text-center">
                                                    <Switch
                                                        checked={each?.toggleHide}
                                                        onCheckedChange={(checked) =>
                                                            dispatch(updateAthletePlacement({
                                                                id: each.id,
                                                                key: "toggleHide",
                                                                value: checked,
                                                            }))
                                                        }
                                                    />
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>


                            <div className="flex flex-col gap-2">
                                <div className="flex items-center gap-2">
                                    <Label>Team Championships Won</Label>
                                    <Switch
                                        checked={statsFormData?.toggleChampionshipWon}
                                        onCheckedChange={(checked) => handleChangeStatsForm('toggleChampionshipWon', checked)}
                                    />
                                </div>
                                <NumericInput
                                    placeholder="No.of Team Championships Won"
                                    value={statsFormData?.teamChampionshipWon}
                                    onValueChange={(value) => handleChangeStatsForm('teamChampionshipWon', value)}
                                />
                            </div>

                            <div className="flex flex-col gap-2">
                                <div className="flex items-center gap-2">
                                    <Label>Athlete Improvement Rate</Label>
                                    <Switch
                                        checked={statsFormData?.toggleAthleteImprovementRate}
                                        onCheckedChange={(checked) => handleChangeStatsForm('toggleAthleteImprovementRate', checked)}
                                    />
                                </div>
                                <NumericInput
                                    suffix="%"
                                    placeholder="Athlete Improvement Rate..."
                                    value={statsFormData?.athleteImprovementRate}
                                    onValueChange={(value) => handleChangeStatsForm('athleteImprovementRate', value)}
                                />
                            </div>

                            <div className="flex flex-col gap-2">
                                <div className="flex items-center gap-2">
                                    <Label>Athlete Retention Rate</Label>
                                    <Switch
                                        checked={statsFormData?.toggleRetentionRate}
                                        onCheckedChange={(checked) => handleChangeStatsForm('toggleRetentionRate', checked)}
                                    />
                                </div>
                                <NumericInput
                                    suffix="%"
                                    placeholder="Athlete Retention Rate..."
                                    value={statsFormData?.athleteRetentionRate}
                                    onValueChange={(value) => handleChangeStatsForm('athleteRetentionRate', value)}
                                />
                            </div>

                            <div className="flex flex-col gap-2">
                                <div className="flex items-center gap-2">
                                    <Label>Certifications Earned</Label>
                                    <Switch
                                        checked={statsFormData?.toggleCertificationEarned}
                                        onCheckedChange={(checked) => handleChangeStatsForm('toggleCertificationEarned', checked)}
                                    />
                                </div>
                                <NumericInput
                                    placeholder="No.of Certifications Earned..."
                                    value={statsFormData?.certificationsEarned}
                                    onValueChange={(value) => handleChangeStatsForm('certificationsEarned', value)}
                                />
                            </div>

                            <div className="flex flex-col gap-2 col-span-full">
                                <Label>Description</Label>
                                <Textarea className="bg-white border-slate-300"
                                    placeholder="Write here..."
                                    value={statsFormData?.description}
                                    onChange={(e) => handleChangeStatsForm('description', e.target.value)}
                                    maxLength={400}
                                    onInput={(e) => {
                                        const input = e.currentTarget;
                                        if (input.value.length > 400) {
                                            input.value = input.value.slice(0, 400);
                                        }
                                    }}
                                />
                                <div className="flex justify-end">
                                    <span className="text-red-600 text-sm">Max 400 characters</span>
                                </div>
                            </div>
                        </div>

                        <div className="flex justify-center">
                            <Button className="w-24" onClick={handleSaveStatsForm}>Save</Button>
                        </div>
                    </>
                    : null}

            </div>
        </>
    )
}
export default CoachStatsForm