import ClientGuard from "@/components/ClientGuard"
import BackButton from "@/components/common/BackButton"
import Payment from "@/components/premium/Payment"
import { ROLES } from "@/utils/constants"
import { Params } from "next/dist/shared/lib/router/utils/route-matcher"

const BusinessPaymentPage = ({ params }: { params: Params}) => {
    const { planId } = params

    return (
        <ClientGuard allowedRoles={[ROLES.BUSINESS]}>
            <>
                <div className="md:col-span-4 flex flex-col">
                    <BackButton icon={false} className="border-none no-underline" />
                    <h2 className="text-primary text-2xl font-bold text-center">Payment</h2>
                    <Payment planId={planId} />
                </div>
            </>
        </ClientGuard>
    )
}
export default BusinessPaymentPage