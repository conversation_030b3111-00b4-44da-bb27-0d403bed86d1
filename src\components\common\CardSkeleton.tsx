import { Card } from "../ui/card"
import { Skeleton } from "../ui/skeleton"

const CardSkeleton = () => {
    return (
        <>
            <div className="w-full space-y-3 rounded-lg overflow-hidden shadow-md">
                {[...Array(5)].map((_, i) => (
                    <Card key={i + 'plansSkltn'} className="overflow-hidden border border-blue-100">
                        <div className="space-y-2 p-4">
                            {/* Title skeleton */}
                            <Skeleton className="h-6 w-4/5 bg-slate-200" />

                            {/* Description */}
                            <div className="flex flex-col space-y-2 pt-1">
                                <Skeleton className="h-4 w-1/3 bg-slate-200" />
                                <Skeleton className="h-4 w-1/3 bg-slate-200" />
                            </div>

                            {/* Buttons skeleton */}
                            <div className="flex flex-row gap-2 pt-2 w-full">
                                <Skeleton className="h-10 w-full bg-slate-200" />
                                <Skeleton className="h-10 w-full bg-slate-200" />
                            </div>
                        </div>
                    </Card>
                ))}
            </div>
        </>
    )
}
export default CardSkeleton