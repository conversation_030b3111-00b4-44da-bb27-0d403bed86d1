// /src/app/api/get-terms/route.ts
import { NextResponse } from 'next/server';

const URL_MAP: Record<string, string> = {
    athlete: 'https://api.engageathlete.com/api/content/v2/getpage/6',
    coach: 'https://api.engageathlete.com/api/content/v2/getpage/5',
    business: 'https://api.engageathlete.com/api/content/v2/getpage/10',
};

export async function GET(req: Request) {
    const { searchParams } = new URL(req.url);
    const type = searchParams.get('type');

    if (!type || !URL_MAP[type]) {
        return NextResponse.json({ message: 'Invalid user type' }, { status: 400 });
    }

    try {
        const res = await fetch(URL_MAP[type]);
        const data = await res.json();

        return NextResponse.json({
            status: 200,
            data: {
                title: data.data.title,
                content: data.data.content,
            },
        });
    } catch (error) {
        return NextResponse.json({ message: 'Failed to fetch terms' }, { status: 500 });
    }
}
