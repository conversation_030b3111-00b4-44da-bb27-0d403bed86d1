import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from "@/components/ui/tooltip";

const getFileTypeFromUrl = (url: string): "image" | "video" | "pdf" | "unknown" => {
    const ext = url.split('.').pop()?.toLowerCase()?.split("?")[0]; 
    if (!ext) return "unknown";

    if (["jpg", "jpeg", "png", "gif", "webp"].includes(ext)) return "image";
    if (["mp4", "webm", "ogg", "mov", "avi"].includes(ext)) return "video";
    if (ext === "pdf") return "pdf";
    return "unknown";
};

const FilePreview = ({ s3FileLink }: { s3FileLink: string }) => {
    if (!s3FileLink) return null;

    const fileType = getFileTypeFromUrl(s3FileLink);
    const fileName = decodeURIComponent(s3FileLink.split("/").pop() || "file");

    return (
        <div className="flex items-center justify-end">
            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger asChild>
                        {fileType === "image" ? (
                            <a
                                href={s3FileLink}
                                download={fileName}
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                <img
                                    src={s3FileLink}
                                    alt={fileName}
                                    className="object-fill w-full max-w-32 cursor-pointer"
                                    onError={(e) => (e.currentTarget.src = "/img-picture.svg")}
                                />
                            </a>
                        ) : fileType === "video" ? (
                            <a
                                href={s3FileLink}
                                download={fileName}
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                <video
                                    controls
                                    className="w-full cursor-pointer object-fill"
                                    onError={(e) => (e.currentTarget.poster = "/video.svg")}
                                >
                                    <source src={s3FileLink} type="video/mp4" />
                                    Your browser does not support the video tag.
                                </video>
                            </a>
                        ) : fileType === "pdf" ? (
                            <a
                                href={s3FileLink}
                                download={fileName}
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                <img
                                    src="/pdf-file.svg"
                                    alt={fileName}
                                    className="object-fill w-full h-24 max-w-24 cursor-pointer"
                                    onError={(e) => (e.currentTarget.src = "/pdf-file.svg")}
                                />
                            </a>
                        ) : (
                            <p className="text-gray-600 text-sm px-4 text-center">
                                {s3FileLink}
                            </p>
                        )}
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>{fileName}</p>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>
        </div>
    );
};

export default FilePreview;
