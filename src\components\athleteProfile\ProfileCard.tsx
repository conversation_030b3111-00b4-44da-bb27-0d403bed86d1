'use client'

import { useTokenValues } from '@/hooks/useTokenValues';
import { AppDispatch, RootState } from '@/store';
import { fetchAthleteIntro, putAthleteIntro, } from '@/store/slices/athlete/athleteProfileSlice';
import { agesList, fetchGraduationYears, genderList } from '@/store/slices/commonSlice';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader, PencilLine } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Controller, DefaultValues, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { z } from 'zod';
import ProfileImgUploader from '../common/ProfileImgUploader';
import SearchInput from '../common/SearchInput';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Separator } from '../ui/separator';
import { Textarea } from '../ui/textarea';

const singleSelectSchema = z
    .object({
        value: z.number({ required_error: "Value is required" }),
        label: z.string({ required_error: "Label is required" }),
    })
    .refine((val) => !!val.value && val.label !== '', {
        message: 'required',
    });


const athleteProfileSchema = z.object({
    firstName: z.string().min(1, 'Athlete preferred name is required'),
    lastName: z.any().optional(),
    blurb: z
        .string()
        .min(10, 'Blurb must be at least 10 characters')
        .max(200, 'Blurb must be at most 200 characters')
        .or(z.null()),
    ageGroup: z.any().optional(),
    gender: z.any().optional(),
    profileImage: z.any().optional(),
});

type FormData = z.infer<typeof athleteProfileSchema>;

const ProfileCard = () => {
    const dispatch = useDispatch<AppDispatch>()
    const { profileCardData, apiStatus, athleteLearning } = useSelector((state: RootState) => state.athleteProfile);
    const [showFull, setShowFull] = useState(false);
    const [isProfileEditable, setIsProfileEditable] = useState(true)
    const { roleId, isPremiumUser } = useTokenValues()

    const initialFetches = async () => {
        await dispatch(fetchGraduationYears())
        try {
            const result = await dispatch(fetchAthleteIntro())
            if (fetchAthleteIntro.rejected.match(result)) {
                toast.error("Failed to fetch profile Info");
            }
        } catch (error) {
            toast.error("Failed to fetch profile Info");
        }
    }

    useEffect(() => {
        initialFetches()
    }, [dispatch])

    const {
        control,
        handleSubmit,
        reset,
        watch,
        formState: { errors },
    } = useForm<FormData>({
        resolver: zodResolver(athleteProfileSchema),
        defaultValues: profileCardData as DefaultValues<FormData>,
    });

    const blurbText = watch('blurb')

    useEffect(() => {
        reset();
    }, [reset]);


    useEffect(() => {
        if (profileCardData) {
            reset(profileCardData as DefaultValues<FormData>);
            setIsProfileEditable(false)
        }
    }, [profileCardData, reset]);

    const onSubmit = async (data: FormData) => {
        const payload = {
            preferredAthleteName: data?.firstName,
            lastName: data?.lastName,
            profileImg: data?.profileImage,
            blurb: data?.blurb,
            gender: data?.gender?.label,
            ageGroupId: data?.ageGroup?.value,
        }
        const resutlAction = await dispatch(putAthleteIntro(payload))
        if (putAthleteIntro.fulfilled.match(resutlAction)) {
            await dispatch(fetchAthleteIntro())
            reset()
            setIsProfileEditable(false)
        }
    };

    const onError = (errors: any) => {
        console.error("Form validation errors:", errors);
    };

    const handleClickCancel = () => {
        reset();
        setIsProfileEditable(false)
    };

    return (
        <div className="bg-slate-100 rounded-lg p-5">
            <form onSubmit={handleSubmit(onSubmit, onError)} className="flex flex-col gap-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 justify-center items-center gap-6">
                    <div className="flex flex-col items-center gap-3">
                        {roleId && isPremiumUser ?
                            <p className="text-md font-bold text-center text-secondary">
                                Premium
                            </p> :
                            null
                        }
                        {isProfileEditable ? <Controller
                            name="profileImage"
                            control={control}
                            render={({ field }) => (
                                <ProfileImgUploader
                                    initialUrl={field.value || '/user.svg'}
                                    onChange={field.onChange}
                                />
                            )}
                        /> :
                            <div className="bg-gradient-to-tr from-slate-300 to-slate-200 p-2 rounded-full">
                                <img
                                    src={profileCardData?.profileImage ? profileCardData?.profileImage : '/user.svg'}
                                    alt="Profile"
                                    className="h-32 w-32 rounded-full object-contain border-2"
                                    onError={(e) => e.currentTarget.src = "/user.svg"}
                                />
                            </div>}
                    </div>

                    {isProfileEditable ?
                        <div className="lg:col-span-2 flex flex-col gap-4">
                            <div className='grid grid-cols-1 md:grid-cols-2 items-end gap-3 w-full'>
                                <div className='w-full'>
                                    <Controller
                                        name="firstName"
                                        control={control}
                                        render={({ field }) =>
                                            <div className='flex flex-col gap-1'>
                                                <Label>Athlete Preferred Name</Label>
                                                <Input {...field} placeholder="Athlete Prefered Name" className='w-full' />
                                            </div>
                                        }
                                    />
                                    {errors.firstName && <p className="text-red-500  text-sm">{errors.firstName.message}</p>}
                                </div>

                                <div className='w-full'>
                                    <Controller
                                        name="lastName"
                                        control={control}
                                        render={({ field }) =>
                                            <div className='flex flex-col gap-1'>
                                                <Label>Last Name</Label>
                                                <Input {...field} placeholder="Last Name" className='w-full' />
                                            </div>
                                        }
                                    />
                                </div>
                            </div>

                            <div>
                                <Controller
                                    name="blurb"
                                    control={control}
                                    render={({ field }) =>
                                        <div className='flex flex-col gap-1'>
                                            <Label>Blurb</Label>
                                            <Textarea
                                                {...field}
                                                value={field.value ?? ""}
                                                rows={3}
                                                maxLength={200}
                                                className='bg-white resize-none border-2 border-slate-300'
                                                placeholder="Write Blurb here..."
                                            />
                                            <p className="text-red-500 text-xs self-end">Max 200 chars.</p>
                                        </div>
                                    }
                                />
                                {errors.blurb && <p className="text-red-500 text-sm">{errors.blurb.message}</p>}
                            </div>

                            <div className='grid grid-cols-1 md:grid-cols-2 items-center gap-4 w-full'>
                                <div className='w-full'>
                                    <Label>Gender</Label>
                                    <Controller
                                        name="gender"
                                        control={control}
                                        render={({ field }) =>
                                            <SearchInput
                                                list={genderList}
                                                name="gender"
                                                value={field.value}
                                                onChange={(name, select) => {
                                                    field.onChange(select)
                                                }}
                                            />
                                        }
                                    />
                                </div>

                                <div className='w-full'>
                                    <Label>Age Group</Label>
                                    <Controller
                                        name="ageGroup"
                                        control={control}
                                        render={({ field }) =>
                                            <SearchInput
                                                list={agesList}
                                                name="ageGroup"
                                                value={field.value}
                                                onChange={(name, selected) => {
                                                    field.onChange(selected)
                                                }} />
                                        }
                                    />
                                </div>
                            </div>
                        </div>
                        :
                        <div className="lg:col-span-2 flex flex-col gap-4">
                            <div className='flex items-center justify-between gap-4'>
                                <div className='flex items-center justify-between gap-4'>
                                    <p>{profileCardData?.firstName}</p>
                                    <p>{profileCardData?.lastName}</p>
                                </div>
                                {!isProfileEditable && <div className="flex justify-end mb-2">
                                    <Button type="button" variant="outline" size="icon" onClick={() => setIsProfileEditable(!isProfileEditable)}>
                                        <PencilLine className="h-4 w-4" />
                                    </Button>
                                </div>}
                            </div>

                            <div className="bg-slate-200 p-4 rounded-lg text-gray-800 break-words">
                                {profileCardData?.blurb?.length! > 100 && !showFull
                                    ? `${profileCardData?.blurb?.slice(0, 100)}...`
                                    : profileCardData?.blurb || 'Share your short story here...'}
                                {profileCardData?.blurb?.length! > 100 && (
                                    <button type="button" onClick={() => setShowFull(!showFull)} className="text-blue-500 ml-2 text-sm">
                                        {showFull ? 'Show less' : 'Read more'}
                                    </button>
                                )}
                            </div>

                            <div className='grid grid-cols-1 md:grid-cols-2 items-center gap-6 w-full'>
                                <div className='flex flex-col gap-1'>
                                    <Label className='font-semibold'>Gender</Label>
                                    <p>{profileCardData?.gender?.label || 'Not Specified'}</p>
                                </div>

                                <div className='flex flex-col gap-1'>
                                    <Label className='font-semibold'>Age</Label>
                                    <p>{profileCardData?.ageGroup?.label || 'Not Specified'}</p>
                                </div>

                                {(athleteLearning?.currentSchoolName?.label || athleteLearning?.otherSchoolName) ? <div className='flex flex-col gap-1'>
                                    <Label className='font-semibold'>School Name</Label>
                                    <p>{athleteLearning?.currentSchoolName?.label || athleteLearning?.otherSchoolName || 'Not Specified'}</p>
                                </div> : null}

                                {athleteLearning?.graduationYear ? <div className='flex flex-col gap-1'>
                                    <Label className='font-semibold'>Class of (Grad Year)</Label>
                                    <p>{athleteLearning?.graduationYear || 'Not Specified'}</p>
                                </div> : null}
                            </div>
                        </div>
                    }
                </div>

                <Separator />

                <div className="flex justify-center">
                    <p className="text-secondary font-semibold">
                        <span className='text-primary'>Primary sports: </span>
                        {profileCardData?.primarySportsList?.join(" | ") || 'Not Specified'}
                    </p>
                </div>

                {isProfileEditable && (
                    <div className="flex justify-end gap-4">
                        <Button variant="outline" type="button" onClick={handleClickCancel}>
                            Cancel
                        </Button>
                        <Button type='submit' disabled={apiStatus === 'putIntroPending'}>
                            {apiStatus === 'putIntroPending' ? (
                                <>
                                    <Loader className='mr-2 h-4 w-4 animate-spin' />
                                </>
                            ) : (
                                'Save'
                            )}
                        </Button>
                    </div>
                )}
            </form>
        </div>
    );
};

export default ProfileCard;