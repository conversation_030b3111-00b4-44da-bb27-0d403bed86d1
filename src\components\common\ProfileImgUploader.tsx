import { Pencil, X } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import { BiSolidPencil } from "react-icons/bi";
interface ProfileImgUploaderProps {
    initialUrl?: string;
    onChange?: (base64: string | null) => void;
}

const ProfileImgUploader: React.FC<ProfileImgUploaderProps> = ({
    initialUrl = "/user.svg",
    onChange,
}) => {
    const [previewUrl, setPreviewUrl] = useState<string>(initialUrl);
    const inputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        setPreviewUrl(initialUrl);
    }, [initialUrl]);

    const convertToBase64 = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = error => reject(error);
        });
    };

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            try {
                const base64 = await convertToBase64(file);
                setPreviewUrl(base64);
                onChange?.(base64);
            } catch (error) {
                console.error("Error converting to Base64", error);
                onChange?.(null);
            }
        }
    };

    const handleRemoveImage = () => {
        onChange?.(null);
    };

    return (
        <div className="flex flex-col items-center gap-2">
            <input
                type="file"
                accept="image/*"
                ref={inputRef}
                onChange={handleFileChange}
                className="hidden"
            />
            <div className={`relative group cursor-pointer`}                           >
                <div onClick={() => inputRef.current?.click()}>
                    <img
                        src={previewUrl}
                        alt="Profile"
                        className="h-36 w-36 rounded-full object-contain border-2"
                        onError={(e) => e.currentTarget.src = "/user.svg"}
                    />
                </div>

                {!previewUrl.includes("/user.svg") ?
                    <button
                        type='button'
                        onClick={handleRemoveImage}
                        className='absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300'>
                        <X size={16} />
                    </button> :
                    <button
                        type='button'
                        className='absolute bottom-4 right-2 text-white -rotate-6 p-1 rounded-full'>
                        <BiSolidPencil className="w-6 h-5" />
                    </button>
                }
            </div>
        </div>
    );
};

export default ProfileImgUploader;
