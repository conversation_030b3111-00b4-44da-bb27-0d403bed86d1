'use client'

import {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>Content,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip";
import { ReactNode } from "react";

interface IProps {
    trigger: ReactNode;
    toolTipText: string;
}

const CommonToolTip = ({ trigger, toolTipText }: IProps) => {

    return (
        <>
            <div>
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger>
                            {trigger}
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>{toolTipText}</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
        </>
    )
}
export default CommonToolTip