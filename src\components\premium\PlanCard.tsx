import { useTokenValues } from "@/hooks/useTokenValues";
import { ROLES } from "@/utils/constants";
import { EachPremiumPlan } from "@/utils/interfaces";
import { motion } from "framer-motion";
import Link from "next/link";
import { Button } from "../ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "../ui/card";

interface IProps {
    item: EachPremiumPlan;
}

function stripHtml(html: string): string {
    const temporalDivElement = document.createElement("div");
    temporalDivElement.innerHTML = html;
    return temporalDivElement.textContent || temporalDivElement.innerText || "";
}


const PlanCard = ({ item }: IProps) => {
    const { roleId } = useTokenValues()

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="w-full h-full"
        >
            <Card className="h-[250px] w-full flex flex-col justify-between shadow-md hover:shadow-2xl transition-shadow duration-300 border border-gray-200">
                <CardHeader>
                    <CardTitle className="text-secondary font-bold text-xl line-clamp-2">
                        {item?.subscriptionName}
                    </CardTitle>
                </CardHeader>

                <CardContent className="flex-1 overflow-hidden">
                    <p className="text-md text-primary line-clamp-4 leading-relaxed">
                        {stripHtml(item?.description)}
                    </p>
                </CardContent>

                <CardFooter className="flex justify-between items-end">
                    <h3 className="text-xl font-bold text-primary">${item?.subscriptionAmount}/{item?.duration_days} days</h3>
                    <Link href={roleId === ROLES.ATHLETE ? `/athlete/premium-plan/checkout/${item?.id}` : `/business/premium-plan/checkout/${item?.id}`}>
                        <Button>Checkout</Button>
                    </Link>
                </CardFooter>
            </Card>
        </motion.div>
    )
}
export default PlanCard