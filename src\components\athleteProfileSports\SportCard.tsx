'use client'
import SearchInput from "@/components/common/SearchInput"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { useTokenValues } from "@/hooks/useTokenValues"
import { AppDispatch, RootState } from "@/store"
import { fetchAthleteSportsByUserSportId, putAthleteSportInfo } from "@/store/slices/athlete/athleteSportProfileSlice"
import { currentSeasonStatusList, fetchAllSpecialities, fetchAllSportLevels, fetchAllSports } from "@/store/slices/commonSlice"
import { zodResolver } from "@hookform/resolvers/zod"
import { Loader, PencilLine } from "lucide-react"
import { useEffect, useState } from "react"
import { Controller, DefaultValues, useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { z } from "zod"
import MultiSelectWithChip from "../common/MultiSelectWithChip"

const sportFormSchema = z.object({
    selectedSport: z
        .object({
            value: z.number(),
            label: z.string(),
        })
        .nullable()
        .refine((val) => val !== null, {
            message: "Sport is required",
        }),
    isPrimarySport: z.boolean().optional(),
    yearsPlayed: z.string().optional(),
    currentSeason: z.any().optional(),
    selectedSportLevel: z.any().optional(),
    addedSportSpecilitiesList: z.any().optional(),
    currentTeam: z.string().optional(),
})

type SportFormValues = z.infer<typeof sportFormSchema>

const SportCard = ({ params }: any) => {
    const [isEditable, setIsEditable] = useState(false)
    const dispatch = useDispatch<AppDispatch>()
    const { allSportsList, allSportLevelList, allSpecilitiesList } = useSelector((state: RootState) => state.commonSlice);
    const { sportFormData, apiStatus } = useSelector((state: RootState) => state.athleteSportProfile)
    const { userId } = useTokenValues()

    const {
        control,
        handleSubmit,
        watch,
        reset,
        formState: { errors }
    } = useForm<SportFormValues>({
        resolver: zodResolver(sportFormSchema),
        defaultValues: sportFormData as DefaultValues<SportFormValues>,
    })
    const selectedSportValue = watch("selectedSport")

    const initialFetches = async () => {
        await dispatch(fetchAthleteSportsByUserSportId(params?.sportId))
        await dispatch(fetchAllSports())
        await dispatch(fetchAllSportLevels())
    }

    useEffect(() => {
        initialFetches()
    }, [dispatch])

    useEffect(() => {
        selectedSportValue?.value && dispatch(fetchAllSpecialities(selectedSportValue.value))
    }, [selectedSportValue?.value, dispatch])

    useEffect(() => {
        if (sportFormData) {
            reset(sportFormData as any)
        }
    }, [reset, sportFormData])

    const onSubmit = async (data: SportFormValues) => {
        const payload = {
            userId,
            sportId: data?.selectedSport?.value,
            primarySport: data?.isPrimarySport,
            yearsPlayed: Number(data?.yearsPlayed || 0),
            currentTeam: data?.currentTeam,
            currentSeasonStatus: data?.currentSeason,
            levelId: data?.selectedSportLevel?.value,
            specialityIds: data?.addedSportSpecilitiesList?.map(each => each?.value)
        }

        const resultAction = await dispatch(putAthleteSportInfo({ payload, sportId: params?.sportId }))
        if (putAthleteSportInfo.fulfilled.match(resultAction)) {
            console.log(resultAction.payload)
            await dispatch(fetchAthleteSportsByUserSportId(params?.sportId))
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <div className="flex flex-col gap-4 bg-slate-100 rounded-md p-4">
                {params.sportName === 'addSport' ? null :
                    <Button size="icon" variant="outline" type="button" className="self-end" onClick={() => setIsEditable(true)}>
                        <PencilLine />
                    </Button>}

                {isEditable ?
                    <>
                        <div className="grid grid-cols-1 md:grid-cols-2 items-end gap-8 mb-3">
                            <div className=" flex flex-col gap-1 w-full">
                                <Label>Sport Name</Label>
                                <Controller
                                    name="selectedSport"
                                    control={control}
                                    render={({ field }) => (
                                        <SearchInput
                                            {...field}
                                            placeholder="Select Sport Name"
                                            list={allSportsList}
                                            value={field?.value}
                                            disable={Boolean(params?.sportId)}
                                            onChange={(name, val) => field.onChange(val)}
                                        />
                                    )}
                                />
                                {errors.selectedSport && <span className="text-red-600 text-sm">{errors.selectedSport.message}</span>}
                            </div>
                            <div className="flex flex-col gap-1">
                                <Label>Primary Sport</Label>
                                <Controller
                                    name="isPrimarySport"
                                    control={control}
                                    render={({ field }) => (
                                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                                    )}
                                />
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 w-full">
                            <div className="flex flex-col gap-1">
                                <Label>Years Played</Label>
                                <Controller
                                    name="yearsPlayed"
                                    control={control}
                                    render={({ field }) => (
                                        <Input
                                            {...field}
                                            className="border-slate-300 bg-white"
                                            placeholder="No. of years played"
                                        />
                                    )}
                                />
                                {errors.yearsPlayed && <span className="text-red-600 text-sm">{errors.yearsPlayed.message}</span>}
                            </div>

                            <div className="flex flex-col gap-1">
                                <Label>Current Season Status</Label>
                                <Controller
                                    name="currentSeason"
                                    control={control}
                                    render={({ field }) => (
                                        <Select onValueChange={(val) => field.onChange(val)}
                                            value={field?.value || ''}>
                                            <SelectTrigger className="bg-white">
                                                <SelectValue placeholder="Select Season Status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {currentSeasonStatusList?.length > 0 ? currentSeasonStatusList?.map(item =>
                                                    <SelectItem value={item} key={item}>{item}</SelectItem>
                                                ) : <SelectItem disabled value="no-options">No Options Found</SelectItem>}
                                            </SelectContent>
                                        </Select>
                                    )}
                                />
                            </div>
                        </div>

                        <div className="grid grid-cols-1 gap-8 w-full">
                            <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-2">
                                <Label className="col-span-1">Level</Label>
                                <div className="flex flex-col col-span-3">
                                    <Controller
                                        name="selectedSportLevel"
                                        control={control}
                                        render={({ field }) => (
                                            <SearchInput
                                                {...field}
                                                list={allSportLevelList}
                                                value={field?.value}
                                                placeholder="Select Sport Level"
                                                onChange={(name, val) => field.onChange(val)}
                                            />
                                        )}
                                    />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-4 items-start gap-2">
                                <Label className="col-span-1 text-wrap mt-1">Position / Speciality</Label>
                                <div className="flex flex-col col-span-3">
                                    <Controller
                                        name="addedSportSpecilitiesList"
                                        control={control}
                                        render={({ field }) => (
                                            <MultiSelectWithChip
                                                {...field}
                                                options={allSpecilitiesList}
                                                placeholder="Select Specialities..."
                                                onChange={field.onChange}
                                                value={field.value || sportFormData?.addedSportSpecilitiesList}
                                                name="addedSportSpecilitiesList"
                                            />
                                        )}
                                    />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-2">
                                <Label className="col-span-1">Current Team</Label>
                                <div className="flex flex-col col-span-3">
                                    <Controller
                                        name="currentTeam"
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                {...field}
                                                className="border-slate-300 text-primary placeholder-gray-800 bg-white"
                                                placeholder="Current Team"
                                            />
                                        )}
                                    />
                                </div>
                            </div>

                            <div className="flex gap-3 justify-end mt-4">
                                <Button variant="outline" type="button" onClick={() => {
                                    reset()
                                    setIsEditable(false)
                                }}>Cancel</Button>
                                <Button type='submit'>
                                    {apiStatus === 'sportInfoPending' ? (
                                        <Loader className='mr-2 h-4 w-4 animate-spin' />
                                    ) : (
                                        'Save'
                                    )}
                                </Button>
                            </div>
                        </div>
                    </> :
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
                        <div className='flex flex-col gap-1'>
                            <Label className='font-semibold'>Sport Name</Label>
                            <p>{sportFormData?.selectedSport?.label || 'Not Specified'}</p>
                        </div>
                        <div className='flex flex-col gap-1'>
                            <Label className='font-semibold'>Primary Sport</Label>
                            <p>{sportFormData?.isPrimarySport ? "Yes" : "No"}</p>
                        </div>
                        <div className='flex flex-col gap-1'>
                            <Label className='font-semibold'>Years Played</Label>
                            <p>{sportFormData?.yearsPlayed || 'Not Specified'}</p>
                        </div>
                        <div className='flex flex-col gap-1'>
                            <Label className='font-semibold'>Current Season Status</Label>
                            <p>{sportFormData?.currentSeason || 'Not Specified'}</p>
                        </div>
                        <div className='flex flex-col gap-1'>
                            <Label className='font-semibold'>Current Team</Label>
                            <p>{sportFormData?.currentTeam || 'Not Specified'}</p>
                        </div>
                        <div className='flex flex-col gap-1'>
                            <Label className='font-semibold'>Level</Label>
                            <p>{sportFormData?.selectedSportLevel?.label || 'Not Specified'}</p>
                        </div>

                        <div className='flex flex-col gap-1'>
                            <Label className='font-semibold'>Position/Specilities</Label>
                            <p>{sportFormData?.addedSportSpecilitiesList?.length! > 0 ? sportFormData?.addedSportSpecilitiesList?.map(each => each?.label)?.join(' , ') : 'Not Specified'}</p>
                        </div>
                    </div>}
            </div>
        </form>
    )
}

export default SportCard