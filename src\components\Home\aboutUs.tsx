"use client";
import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import parse from "html-react-parser";
import {
  titleParseOptions,
  shortDescriptionParseOptions,
  fileDescriptionParseOptions
} from "@/utils/parseOptions";

const AboutSection = () => {
  const { aboutSection, aboutVideo, loading } = useSelector(
    (state: RootState) => state.homeCMS
  );

  if (loading || !aboutSection) return null;

  return (
    <section
      id="about"
      className="py-20 bg-[#0D1D3A] px-3 sm:px-8 md:px-16 lg:px-36 xl:px-56"
    >
      {/* Title */}
      <h2 className="text-3xl text-center font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-6">
        {parse(aboutSection.title || "", titleParseOptions)}
      </h2>

      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row gap-6 items-stretch">
          {/* Video Side */}
          <div className="flex-[1.3]">
            {aboutVideo?.[0]?.fileLocation && (
              <video
                className="w-full h-full rounded-lg shadow-lg object-cover"
                autoPlay
                muted
                loop
                playsInline
              >
                <source src={aboutVideo[0].fileLocation} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            )}
          </div>

          {/* Text Side */}
          <div className="flex-[1] space-y-6 flex flex-col justify-center text-gray-200">
            <div className="cms-text space-y-4">
              {parse(
                aboutVideo?.[0]?.fileDescription || "",
                fileDescriptionParseOptions
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
