"use client";
import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { titleParseOptions } from "@/utils/parseOptions";
import parse from "html-react-parser";

const HomeAnnouncement = () => {
  const { homeAnnouncementSection, loading } = useSelector(
    (state: RootState) => state.homeCMS
  );

  if (loading || !homeAnnouncementSection?.title) return null;

  return (
    <div className="w-full p-2 h-14 relative bg-[#0D1D3A] overflow-hidden text-white">
      {/* Gradient overlays */}
      <div className="absolute top-0 left-0 w-16 h-full z-10 bg-gradient-to-r from-[#0D1D3A] to-transparent pointer-events-none" />
      <div className="absolute top-0 right-0 w-16 h-full z-10 bg-gradient-to-l from-[#0D1D3A] to-transparent pointer-events-none" />

      {/* Marquee text */}
      <div className="absolute whitespace-nowrap animate-marquee hover:[animation-play-state:paused] transition-transform duration-[20s] ease-linear px-4 text-sm font-medium">
        {parse(homeAnnouncementSection.title, titleParseOptions)}
      </div>
    </div>
  );
};

export default HomeAnnouncement;
