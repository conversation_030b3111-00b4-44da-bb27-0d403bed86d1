"use client"
import AthleteSport from "@/components/athleteProfileSports/AthleteSport";
import { notFound, useParams } from "next/navigation";

const AthleteSportPage = () => {
    const params = useParams();
    const { slug, sportName } = params
    const validSlugPattern = /^(\d+)-([a-zA-Z]+)-([a-zA-Z]+)$/;
    if (!validSlugPattern.test(slug as string)) return notFound();
    if (!/^[a-zA-Z]+$/.test(sportName as string)) return notFound();
    // if (!/^\d+$/.test(sportId)) return notFound();

    return (
        <>
            <AthleteSport params={params} />
        </>
    )
}
export default AthleteSportPage