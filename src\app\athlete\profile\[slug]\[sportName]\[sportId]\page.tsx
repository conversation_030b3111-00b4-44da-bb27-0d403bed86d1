import AthleteSport from "@/components/athleteProfileSports/AthleteSport";
import ClientGuard from "@/components/ClientGuard";
import { ROLES } from "@/utils/constants";

type Props = {
    params: {
        slug: string;
        sportName: string;
        sportId: string;
    };
};

const AthleteSportPage = ({ params }: Props) => {

    return (
        <ClientGuard allowedRoles={[ROLES.ATHLETE]}>
            <AthleteSport params={params} />
        </ClientGuard>
    )
}
export default AthleteSportPage