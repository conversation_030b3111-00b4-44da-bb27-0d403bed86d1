"use client";

import { Info, Globe2, Workflow, Users, Menu, X } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import Login from "./auth/Login";
import { But<PERSON> } from "./ui/button";

const navList = [
  { id: 1, name: "About", route: "#about", icon: <Info size={20} /> },
  { id: 2, name: "Ecosystem", route: "#ecosystem", icon: <Globe2 size={20} /> },
  {
    id: 3,
    name: "How It Works",
    route: "#how-it-works",
    icon: <Workflow size={20} />,
  },
  {
    id: 4,
    name: "Community",
    route: "#connect-athlete",
    icon: <Users size={20} />,
  },
];

const AppBar = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [showSignUp, setShowSignUp] = useState(false);

  useEffect(() => {
    document.body.style.overflow = menuOpen ? "hidden" : "";
  }, [menuOpen]);

  const handleNavClick = (e: any, route: string) => {
    e.preventDefault();
    const el = document.querySelector(route);
    if (el) {
      el.scrollIntoView({ behavior: "smooth", block: "start" });
      setMenuOpen(false);
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      const hero = document.getElementById("hero");
      if (hero) {
        const rect = hero.getBoundingClientRect();
        setShowSignUp(rect.bottom <= 80); // navbar height buffer
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <>
      {/* Top Fixed, Centered Navbar */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-[#0D1D3A] lg:top-4 lg:left-1/2 lg:-translate-x-1/2 lg:w-[95%] lg:max-w-[1200px] lg:bg-transparent lg:rounded-xl">
        <div className="flex justify-between items-center px-4 sm:px-8 md:px-12 h-16">
          {/* Logo */}
          <Link href="/">
            <img
              src="/connectathlete-logo.svg"
              alt="Logo"
              className="h-20 w-40"
            />
          </Link>

          {/* Desktop Nav Icons */}
          <div className="hidden sm:flex items-center gap-6">
            {navList.map((item) => (
              <button
                key={item.id}
                onClick={(e) => handleNavClick(e, item.route)}
                className="bg-orange-500 p-2 rounded-[4px] text-gray-800 hover:text-white transition"
                title={item.name}
              >
                {item.icon}
              </button>
            ))}

            {/* CTA */}

            <Login />
            {showSignUp && (
              <div className="text-center">
                <Button
                  onClick={(e) => handleNavClick(e, "#registration")}
                  variant={"primaryGradient"}
                  className="hover:shadow-2xl"
                >
                  Sign up For Free
                </Button>
              </div>
            )}
          </div>

          {/* Mobile Hamburger */}
          <div className="sm:hidden flex items-center">
            <button onClick={() => setMenuOpen(!menuOpen)}>
              {menuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>
      </nav>

      {/* Mobile Drawer */}
      <div
        className={`fixed z-50 inset-0 transition-transform duration-500 bg-[#0D1D3A] text-white sm:hidden
    ${menuOpen ? "translate-x-0" : "translate-x-full"}`}
      >
        <div className="flex justify-between items-center px-6 pt-6">
          <img
            src="/connectathlete-logo.svg"
            alt="Logo"
            className="h-8 w-auto"
          />
          <button onClick={() => setMenuOpen(false)}>
            <X size={28} className="text-white" />
          </button>
        </div>

        <div className="flex flex-col items-center justify-center h-[calc(100vh-80px)] gap-8 px-6">
          {navList.map((item) => (
            <button
              key={item.id}
              onClick={(e) => handleNavClick(e, item.route)}
              className="flex items-center gap-3 text-lg font-medium text-white hover:text-orange-400 transition"
            >
              {item.icon}
              {item.name}
            </button>
          ))}

          <div className="w-full mt-4">
            <Login />
          </div>
        </div>
      </div>
    </>
  );
};

export default AppBar;
