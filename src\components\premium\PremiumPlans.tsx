'use client'
import { AppDispatch, RootState } from "@/store"
import { fetchPremiumPlans } from "@/store/slices/premiumSlice"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import CardSkeleton from "../common/CardSkeleton"
import { Button } from "../ui/button"
import PlanCard from "./PlanCard"

const PremiumPlans = () => {
    const { premiumPlansList, apiStatus } = useSelector((state: RootState) => state.premium)
    const dispatch = useDispatch<AppDispatch>()
    const router = useRouter()

    const handleFetchPlans = async () => {
        await dispatch(fetchPremiumPlans())
    }

    useEffect(() => {
        handleFetchPlans()
    }, [dispatch])

    if (apiStatus === 'plansLoading') {
        return (
            <CardSkeleton />
        );
    }

    if (apiStatus === 'plansFailed') {
        return (
            <div className="flex flex-col items-center justify-center h-full py-10 text-center">
                <p className="text-xl font-semibold text-red-600 mb-2">Failed to load plans</p>
                <p className="text-sm text-gray-500 mb-4">Something went wrong while fetching the plans. Please try again.</p>
                <Button onClick={handleFetchPlans}>
                    Retry
                </Button>
            </div>
        );
    }


    return (
        <>
            <div>
                {premiumPlansList && premiumPlansList!?.length > 0 ? (
                    <div className="flex flex-col gap-4">
                        <div className="grid grid-cols-1 gap-6">
                            {premiumPlansList?.map((each) => (
                                <PlanCard key={each?.id} item={each} />
                            ))}
                        </div>
                    </div>
                ) : (
                    <div className="flex flex-col items-center justify-center gap-4 py-16 text-center text-muted-foreground">
                        <div className="text-5xl">📄</div>
                        <h2 className="text-xl font-semibold">No plans available yet</h2>
                        <p className="max-w-md text-sm">
                            It looks like you don’t have any plans set up for your profile.
                        </p>
                    </div>
                )}
            </div>

        </>
    )
}
export default PremiumPlans