import CommonCalender from "@/components/common/CommonCalender"
import UploadFiles from "@/components/common/UploadFiles"
import { useHandleAthleteSectionExpose } from "@/hooks/useAthleteExposeSections"
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"
import { useTokenValues } from "@/hooks/useTokenValues"
import { AppDispatch, RootState } from "@/store"
import { deleteAthleteAchievements, fetchAthleteAchievement, handleUpdateUserInput, postAthleteAchievement, putAthleteAchievements } from "@/store/slices/athlete/athleteProfileSlice"
import { fetchTags } from "@/store/slices/commonSlice"
import { ROLES } from "@/utils/constants"
import { preventSpaces } from "@/utils/validations"
import { zodResolver } from '@hookform/resolvers/zod'
import { format } from "date-fns"
import { Loader } from "lucide-react"
import { useEffect, useState } from "react"
import { Controller, useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { z } from 'zod'
import AchievementItem from "../common/AchievementItem"
import CommonToolTip from "../common/CommonToolTip"
import MultiSelectWithChip from "../common/MultiSelectWithChip"
import UpgradePremiumSection from "../common/UpgradePremiumSection"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import { Switch } from "../ui/switch"
import { Textarea } from "../ui/textarea"


const schema = z
    .object({
        title: z
            .string()
            .min(1, 'Title is required'),
        date: z.date({
            required_error: "Date is required.",
        }),
        blurb: z.string().optional(),
        link: z.string().min(1, 'Link is required').url('Invalid url'),
        tags: z
            .array(
                z.object({
                    value: z.number().min(1),
                    label: z.string().min(1),
                })
            )
            .min(1, 'Please select at least one tag')
            .max(3, 'You can select up to 3 tags'),
        file: z.any().optional()
    })


const Achievements = () => {
    const { allTagsList } = useSelector((state: RootState) => state.commonSlice)
    const { toggleAchievements, achievementData, addedAchievementsList, apiStatus } = useSelector((state: RootState) => state.athleteProfile)
    const [achievementId, setAchievementId] = useState<null | number>(null)
    const dispatch = useDispatch<AppDispatch>()
    type FormData = z.infer<typeof schema>;
    const { profileId, } = useLocalStoredInfo()
    const { roleId, userId, isPremiumUser } = useTokenValues()
    const { handleToggleSections } = useHandleAthleteSectionExpose()
    const isAthletePremiumUser = roleId === ROLES.ATHLETE && isPremiumUser

    useEffect(() => {
        dispatch(fetchTags())
        dispatch(fetchAthleteAchievement())
    }, [dispatch])

    const {
        control,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<FormData>({
        resolver: zodResolver(schema),
        defaultValues: {
            ...achievementData,
            title: achievementData?.title ?? '',
            link: achievementData?.link ?? '',
            blurb: achievementData?.blurb ?? '',
            tags: achievementData?.tags ?? [],
            file: achievementData?.file ?? '' as any,
            date: achievementData?.date ? new Date(achievementData.date) : undefined,
        }
    });

    useEffect(() => {
        if (achievementData) {
            reset({
                ...achievementData,
                title: achievementData?.title ?? '',
                link: achievementData?.link ?? '',
                blurb: achievementData?.blurb ?? '',
                tags: achievementData?.tags ?? [],
                file: achievementData?.file ?? '' as any,
                date: achievementData?.date ? new Date(achievementData.date) : undefined,
            });
        }
    }, [achievementData, reset]);

    const handleCancle = () => {
        dispatch(handleUpdateUserInput({ name: 'achievementData', value: null }))
        setAchievementId(null)
        reset({})
    }

    const handleEditAchievement = (id: number) => {
        const selectedAchievement = addedAchievementsList?.find(each => each?.id === id)
        dispatch(handleUpdateUserInput({ name: 'achievementData', value: selectedAchievement }))
        setAchievementId(id)
    }

    const handleDeleteAchievement = async (id: number) => {
        try {
            const resultAction = await dispatch(deleteAthleteAchievements(id))
            if (deleteAthleteAchievements.fulfilled.match(resultAction)) {
                await dispatch(fetchAthleteAchievement())
            }
        } catch (error) {
            console.log(error)
        }
    }

    const postAchievement = async (payload) => {
        try {
            const resultAction = await dispatch(postAthleteAchievement(payload))
            if (postAthleteAchievement.fulfilled.match(resultAction)) {
                await dispatch(fetchAthleteAchievement())
                handleCancle()
            }
        } catch (error) {
            console.log(error)
        }
    }

    const editAchieement = async (payload) => {
        try {
            const resultAction = await dispatch(putAthleteAchievements(payload))
            if (putAthleteAchievements.fulfilled.match(resultAction)) {
                handleCancle()
                await dispatch(fetchAthleteAchievement())
                setAchievementId(null)
                reset({})
            }
        } catch (error) {
            console.log(error)
        }
    }

    const onSubmit = async (data: FormData) => {
        const payload = {
            roleId,
            athleteId: profileId,
            userId,
            achievementTitle: data?.title,
            achievementDesc: data?.blurb,
            achievementDate: format(new Date(data?.date), 'yyyy-MM-dd'),
            achievementLink: data?.link,
            s3FileLink: data?.file,
            genTagId1: data?.tags?.length >= 1 ? data?.tags?.[0].value : null,
            genTagId2: data?.tags?.length >= 2 ? data?.tags?.[1].value : null,
            genTagId3: data?.tags?.length >= 3 ? data?.tags?.[2].value : null,
            isHidden: !toggleAchievements
        }

        if (achievementId) {
            editAchieement(payload)
        } else {
            postAchievement(payload)
        }
    };

    const onError = (errors: any) => {
        console.error("Form validation errors:", errors);
    };

    return (
        <>
            <div className="flex flex-col gap-5 p-4 rounded-lg bg-slate-100">
                {!isPremiumUser && <UpgradePremiumSection />}

                <div className="flex flex-col gap-1">
                    <div className="flex gap-3 justify-center items-center">
                        <h3 className="font-bold text-xl">Overall Achievements (Off The Field) </h3>
                        <CommonToolTip
                            trigger={<Switch disabled={!isAthletePremiumUser} checked={toggleAchievements} onCheckedChange={(checked) => handleToggleSections('achievements', checked)} />}
                            toolTipText={'Hide/Unhide Achievements'}
                        />
                    </div>
                    <p className="text-center text-wrap">Proudest moments beyond sports — academic excellence, leadership awards,
                        community service, and more.
                    </p>
                </div>

                {toggleAchievements ?
                    <div className="flex flex-col gap-5">
                        <>
                            <form onSubmit={handleSubmit(onSubmit, onError)}>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 justify-center items-center gap-4 px-16">
                                    <div>
                                        <Controller
                                            name='date'
                                            control={control}
                                            render={({ field }) => (
                                                <CommonCalender
                                                    placeholder={`Date`}
                                                    mode='single'
                                                    dateValue={field.value}
                                                    disabled={!isAthletePremiumUser}
                                                    setDateFn={(date) => field.onChange(date)}
                                                />
                                            )}
                                        />
                                        {errors.date && (
                                            <p className='text-red-500 text-sm mt-1'>
                                                {errors.date.message}
                                            </p>
                                        )}
                                    </div>

                                    <div>
                                        <Controller
                                            name='title'
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type={'text'}
                                                    placeholder='Enter Title'
                                                    onChange={(e) => {
                                                        const sanitizedValue = e.target.value
                                                            ?.trimStart()
                                                            ?.replace(preventSpaces, '');
                                                        field.onChange(sanitizedValue);
                                                    }}
                                                    className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                                    disabled={!isAthletePremiumUser}
                                                />
                                            )}
                                        />
                                        {errors.title && (
                                            <p className='text-red-500 text-sm mt-1'>
                                                {errors.title.message}
                                            </p>
                                        )}
                                    </div>

                                    <div>
                                        <Controller
                                            name='link'
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type={'text'}
                                                    placeholder='Enter Link'
                                                    onChange={(e) => {
                                                        const sanitizedValue = e.target.value
                                                            ?.trimStart()
                                                            ?.replace(preventSpaces, '');
                                                        field.onChange(sanitizedValue);
                                                    }}
                                                    className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                                    disabled={!isAthletePremiumUser}
                                                />
                                            )}
                                        />
                                        {errors.link && (
                                            <p className='text-red-500 text-sm mt-1'>
                                                {errors.link.message}
                                            </p>
                                        )}
                                    </div>

                                    <div className="col-span-full">
                                        <Controller
                                            name='blurb'
                                            control={control}
                                            render={({ field }) => (
                                                <Textarea
                                                    {...field}
                                                    placeholder='Write Description...'
                                                    onChange={(e) => {
                                                        const sanitizedValue = e.target.value
                                                            ?.trimStart()
                                                            ?.replace(preventSpaces, '');
                                                        field.onChange(sanitizedValue);
                                                    }}
                                                    rows={3}
                                                    onInput={(e) => {
                                                        const input = e.currentTarget;
                                                        if (input.value.length > 200) {
                                                            input.value = input.value.slice(0, 200);
                                                        }
                                                    }}
                                                    className='bg-white mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                                    disabled={!isAthletePremiumUser}
                                                />
                                            )}
                                        />
                                        <p className="text-right text-destructive self-end text-xs">Max 200 chars</p>
                                        <div className="flex items-center justify-between">
                                            {errors.blurb && (
                                                <p className='text-red-500 text-sm mt-1'>
                                                    {errors.blurb.message}
                                                </p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="col-span-full">
                                        <Controller
                                            name="tags"
                                            control={control}
                                            render={({ field }) => {
                                                return (
                                                    <MultiSelectWithChip
                                                        name="tags"
                                                        control={control}
                                                        options={allTagsList}
                                                        errors={errors}
                                                        placeholder="Select up to 3 tags..."
                                                        maxSelect={3}
                                                        disable={!isAthletePremiumUser}
                                                    />
                                                );
                                            }}
                                        />

                                    </div>

                                    <div className="col-span-full">

                                        <Controller
                                            name="file"
                                            control={control}
                                            render={({ field: { value, onChange } }) => (
                                                <UploadFiles
                                                    acceptType={["application/pdf"]}
                                                    value={value!}
                                                    onFileSelect={(data) => onChange(data)}
                                                    handleRemove={() => onChange(null)}
                                                    className=""
                                                    disable={!isAthletePremiumUser}
                                                />
                                            )}
                                        />
                                    </div>
                                </div>

                                <div className='flex flex-row justify-end mt-4 gap-5'>
                                    <Button disabled={!isAthletePremiumUser} variant={'outline'} className='w-24 border-primary text-primary hover:text-primary' type='button' onClick={handleCancle}>
                                        Cancel
                                    </Button>
                                    <Button disabled={!isAthletePremiumUser || apiStatus === 'achievementPending'} className='w-24' type='submit'>
                                        {apiStatus === 'achievementPending' ? <Loader className='w-4 h-4 animate-spin' /> : 'Save'}
                                    </Button>
                                </div>
                            </form>

                            <div className="max-h-96 overflow-y-auto flex flex-col gap-3">
                                {addedAchievementsList?.length ? addedAchievementsList?.map(each => (
                                    <AchievementItem
                                        key={each?.id + 'achvm'}
                                        item={each}
                                        handleEdit={handleEditAchievement}
                                        handleDelete={handleDeleteAchievement}
                                    />
                                )) : null}
                            </div>
                        </>
                    </div> : null}
            </div>
        </>
    )
}
export default Achievements