"use client";

import { useTokenValues } from "@/hooks/useTokenValues";
import { AppDispatch } from "@/store";
import { logout } from "@/store/slices/auth/loginSlice";
import { jwtDecode } from "jwt-decode";
import { useRouter } from "next/navigation";
import { ReactNode, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import CALoader from "./common/CALoader";

interface ClientGuardProps {
    allowedRoles: number[];
    children: ReactNode;
}

export interface DecodedToken {
    exp: number;
    [key: string]: any;
}

export default function ClientGuard({ allowedRoles, children }: ClientGuardProps) {
    const router = useRouter();
    const dispatch = useDispatch<AppDispatch>();
    const { roleId } = useTokenValues();
    const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);

    const logoutAndClearStorage = () => {
        dispatch(logout());
        localStorage.clear();
        router.push("/");
    };

    useEffect(() => {
        const token = localStorage.getItem("token") || "";

        if (!token) {
            setIsAuthorized(false);
            router.replace("/");
            return;
        }

        if (!roleId) {
            return; // wait for roleId to be available
        }

        const decoded = jwtDecode<DecodedToken>(token);
        const currentTime = Date.now() / 1000;

        if (decoded.exp < currentTime) {
            logoutAndClearStorage();
            return;
        }

        if (allowedRoles.includes(Number(roleId))) {
            setIsAuthorized(true);
        } else {
            setIsAuthorized(false);
            router.push("/404");
        }
    }, [allowedRoles, roleId]);

    if (isAuthorized === null) {
        return <CALoader />
    }

    if (!isAuthorized) {
        return null;
    }

    return <>{children}</>;
}
