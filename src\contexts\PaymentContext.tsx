import { PaymentData } from "@/utils/interfaces";
import { createContext, ReactNode, useContext, useState } from "react";

interface PaymentContextType {
    paymentData: PaymentData | null;
    setProcessPaymentData: (data: PaymentData) => void;
    clearPaymentData: () => void;
}

const PaymentContext = createContext<PaymentContextType | undefined>(undefined);

export const PaymentProvider = ({ children }: { children: ReactNode }) => {
    const [paymentData, setPaymentDataState] = useState<PaymentData | null>(null);

    const setProcessPaymentData = (data: PaymentData) => setPaymentDataState(data);
    const clearPaymentData = () => setPaymentDataState(null);

    return (
        <PaymentContext.Provider value={{ paymentData, setProcessPaymentData, clearPaymentData }}>
            {children}
        </PaymentContext.Provider>
    );
};

export const usePaymentData = () => {
    const context = useContext(PaymentContext);
    if (context === undefined) {
        console.error("usePaymentData must be used within a PaymentProvider");
    }
    return context as PaymentContextType;
};
