'use client';
import {
    Table,
    TableBody,
    TableCell,
    TableRow,
} from "@/components/ui/table";
import { dollarDecimalAmount } from "@/utils/validations";
import { EachTransaction } from "@/utils/interfaces";
import { format } from "date-fns";

interface IProps {
    item: EachTransaction;
}

const OrderSummaryItem = ({ item }: IProps) => {
    const formatDate = (date: string | Date | undefined) =>
        date ? format(new Date(date), "MMM dd, yyyy") : "-";

    const formatAmount = (amount?: number) =>
        dollarDecimalAmount(amount ?? 0, 2);

    const rows: { label: string; value: string | number }[] = [
        { label: "Order ID", value: item?.id || "-" },
        { label: "Payment Date", value: formatDate(item?.paymentDate) },
        { label: "Subscription Plan Name", value: item?.subscriptionPlanName || "-" },
        { label: "Subscription Start Date", value: formatDate(item?.subscriptionStartDate) },
        { label: "Subscription End Date", value: formatDate(item?.subscriptionEndDate) },
        { label: "Next Renewal Date", value: formatDate(item?.renewalDate) },
        { label: "Subscription Amount", value: formatAmount(item?.subscriptionPlanAmount) },
        ...(item?.promoCode
            ? [{ label: "Promo Code Applied", value: item?.promoCode }]
            : []),
        ...(item?.discountAmount > 0
            ? [{ label: "Discount Amount", value: `- $${item?.discountAmount?.toFixed(2)}` }]
            : []),
        ...(item?.netPlanAmount > 0
            ? [{ label: "Net Plan Amount", value: formatAmount(item?.netPlanAmount) }]
            : []),
        ...(item?.processingFees > 0
            ? [{ label: "Processing Fee", value: formatAmount(item?.processingFees) }]
            : []),
        ...(item?.serviceFees > 0
            ? [{ label: "Service Fee", value: formatAmount(item?.serviceFees) }]
            : []),
        ...(item?.convenienceFees > 0
            ? [{ label: "Convenience Fee", value: formatAmount(item?.convenienceFees) }]
            : []),
        ...(item?.taxAmount > 0
            ? [{ label: "Sales Tax Amount", value: formatAmount(item?.taxAmount) }]
            : []),
        { label: "Total Amount", value: formatAmount(item?.totalAmountPaid) },
    ];


    return (
        <div className="overflow-x-auto rounded-2xl border border-gray-200 bg-white shadow-xl">
            <h2 className="p-3 text-lg font-bold text-primary border-b border-gray-200 bg-gray-100">Order Summary</h2>
            <Table className="min-w-[450px]">
                <TableBody>
                    {rows?.map((row, idx) => {
                        const isLastRow = idx === rows?.length - 1;

                        return (
                            <TableRow
                                key={idx + `${row?.value}`}
                                className={
                                    isLastRow
                                        ? "bg-green-50"
                                        : idx % 2 === 1
                                            ? "bg-gray-50"
                                            : ""
                                }
                            >
                                <TableCell
                                    className={
                                        isLastRow
                                            ? "px-6 py-4 text-lg font-bold text-green-600"
                                            : "font-semibold px-6 py-3 text-primary"
                                    }
                                >
                                    {row.label}
                                </TableCell>
                                <TableCell
                                    className={
                                        isLastRow
                                            ? "px-6 py-4 text-right text-lg font-bold text-green-600"
                                            : "px-6 py-3 text-right text-primary"
                                    }
                                >
                                    {row.value}
                                </TableCell>
                            </TableRow>
                        );
                    })}
                </TableBody>
            </Table>
        </div>
    );
};

export default OrderSummaryItem;
