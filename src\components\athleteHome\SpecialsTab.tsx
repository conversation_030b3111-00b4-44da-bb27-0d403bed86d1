import Link from "next/link"
import { <PERSON><PERSON> } from "../ui/button"
import { ListCard } from "./ListCard"


const posts = [
    {
        id: 1,
        author: "<PERSON>",
        role: "Product Designer at Designify",
        content: "Excited to share our latest UI kit!",
        time: "2h",
        avatar: "/user.svg",
        date: "June 5, 2025"
    },
    {
        id: 2,
        author: "<PERSON>",
        role: "Software Engineer at DevCorp",
        content: "Check out my new blog on microservices.",
        time: "5h",
        avatar: "/user.svg",
        date: "June 5, 2025"
    },
    {
        id: 3,
        author: "<PERSON>",
        role: "Software Engineer at DevCorp",
        content: "Check out my new blog on microservices.",
        time: "5h",
        avatar: "/user.svg",
        date: "June 5, 2025"
    },
    {
        id: 4,
        author: "<PERSON>",
        role: "Software Engineer at DevCorp",
        content: "Check out my new blog on microservices.",
        time: "5h",
        avatar: "/user.svg",
        date: "June 5, 2025"
    }
]

const SpecialsTab = () => {
    return (
        <>
            <div className="flex flex-col gap-5">
                <div className="flex justify-end">
                    <Link href={'/admin-specials'}>
                        <Button variant={'link'} className="text-blue-600">View All</Button>
                    </Link>
                </div>
                <div className="flex flex-col gap-4 bg-slate-100 w-full p-3 rounded-md">
                    {posts?.map((post) => (
                        <ListCard key={post.id} {...post} />
                    ))}
                </div>
            </div>
        </>
    )
}
export default SpecialsTab