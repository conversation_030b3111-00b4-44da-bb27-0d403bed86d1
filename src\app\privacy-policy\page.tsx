"use client";
import React, { useEffect, useState } from "react";

const PrivacyPolicy = () => {
  const [content, setContent] = useState<string>("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPrivacy = async () => {
      try {
        const res = await fetch("/api/get-privacy");
        const data = await res.json();
        if (data.status === 200) {
          setContent(data.data.content);
        }
      } catch (err) {
        console.error("Failed to load privacy policy:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchPrivacy();
  }, []);

  return (
    <section className="max-w-4xl mx-auto md:p-8 p-6 mt-16">
      {loading ? (
        <p className="text-center">Loading...</p>
      ) : (
        <div
          className="prose prose-lg max-w-none"
          dangerouslySetInnerHTML={{ __html: content }}
        ></div>
      )}
    </section>
  );
};

export default PrivacyPolicy;
