"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from "@/components/ui/command"
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { EachSearchItem } from "@/utils/interfaces"
import { Check, ChevronDown } from "lucide-react"
import React, { useEffect, useState } from "react"

interface IProps {
    list: EachSearchItem[],
    placeholder?: string,
    name: string;
    className?: string;
    onChange: (name: string, selected: EachSearchItem | null) => void;
    value?: EachSearchItem | null;
    disable?: boolean
}

const SearchInput = ({ list, placeholder, name, className, onChange, value, disable }: IProps) => {
    const [open, setOpen] = useState(false)
    const [commonList, setCommonList] = useState(list || [])
    const [selectedItem, setSelectedItem] = useState<EachSearchItem | null>(null)

    useEffect(() => {
        value && setSelectedItem(value)
    }, [value])

    useEffect(() => {
        list && setCommonList(list)
    }, [list])

    const handleSelect = (selectedValue: string) => {
        const selectedObj = list?.find(item => item.value?.toString() === selectedValue) || null
        setSelectedItem(selectedObj)
        onChange(name, selectedObj)
        setOpen(false)
    }

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    disabled={disable}
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className={cn("w-full flex items-start justify-between bg-white hover:bg-white", className)}
                >
                    {selectedItem?.label || placeholder || 'Select option...'}
                    <ChevronDown className="text-slate-400" />
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full">
                <Command>
                    <CommandInput placeholder={placeholder} />
                    <CommandList>
                        <CommandEmpty>No data found.</CommandEmpty>
                        <CommandGroup>
                            {commonList?.map((item) => (
                                <CommandItem
                                    className="text-wrap max-w-72"
                                    key={item.value}
                                    value={`${item.label?.toString()?.toLowerCase()} ${item?.value?.toString()}`}
                                    onSelect={() => handleSelect(item?.value?.toString())}
                                >
                                    <Check
                                        className={cn(
                                            "mr-2 h-4 w-4",
                                            selectedItem?.value?.toString() === item.value?.toString()
                                                ? "opacity-100"
                                                : "opacity-0"
                                        )}
                                    />
                                    {item.label?.toString()}
                                </CommandItem>
                            ))}

                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    )
}
export default React.memo(SearchInput)