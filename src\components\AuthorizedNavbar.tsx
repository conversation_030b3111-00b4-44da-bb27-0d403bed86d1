"use client";
import { useTokenValues } from "@/hooks/useTokenValues";
import { usePathname } from "next/navigation";
import NavBar from "./NavBar";

const AuthorizedNavbar = () => {
  const pathname = usePathname()
  const { roleId } = useTokenValues()

  return (
    <>
      {roleId && pathname !== '/' && (
        <>
          <div className="mb-[4.5rem]">
            <NavBar />
          </div>
        </>
      )}
    </>
  );
};

export default AuthorizedNavbar;
