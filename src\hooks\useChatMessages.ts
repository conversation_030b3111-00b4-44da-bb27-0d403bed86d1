import { useState, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { ChatMessage, Conversation } from '@/components/messages/MessagesScreen';


export const useChatMessages = () => {
  const [conversations, setConversations] = useState<Conversation[]>();
  const [messages, setMessages] = useState<Record<string, ChatMessage[]>>();

  const sendMessage = useCallback((conversationId: string, content: string, attachment?: { file: File; type: 'image' | 'document' | 'pdf'; url: string }) => {
    const newMessage: ChatMessage = {
      id: uuidv4(),
      senderId: 'current-user',
      senderName: 'You',
      content,
      timestamp: new Date(),
      isRead: true,
      attachment: attachment ? {
        file: attachment.file,
        type: attachment.type,
        url: attachment.url,
        name: attachment.file.name,
        size: attachment.file.size,
      } : undefined,
    };

    // Add message to the conversation
    setMessages(prev => ({
      ...prev,
      [conversationId]: [...(prev[conversationId] || []), newMessage],
    }));

    // Update conversation's last message
    setConversations(prev =>
      prev.map(conv =>
        conv.id === conversationId
          ? { ...conv, lastMessage: newMessage }
          : conv
      )
    );
  }, []);

  const markAsRead = useCallback((conversationId: string) => {
    // Mark all messages in conversation as read
    setMessages(prev => ({
      ...prev,
      [conversationId]: (prev[conversationId] || []).map(msg => ({ ...msg, isRead: true })),
    }));

    // Reset unread count
    setConversations(prev =>
      prev.map(conv =>
        conv.id === conversationId
          ? { ...conv, unreadCount: 0 }
          : conv
      )
    );
  }, []);

  // Sort conversations by last message timestamp
  const sortedConversations = conversations?.sort((a, b) => {
    const aTime = a.lastMessage?.timestamp.getTime() || 0;
    const bTime = b.lastMessage?.timestamp.getTime() || 0;
    return bTime - aTime;
  });

  return {
    conversations: sortedConversations,
    messages,
    sendMessage,
    markAsRead,
  };
};
