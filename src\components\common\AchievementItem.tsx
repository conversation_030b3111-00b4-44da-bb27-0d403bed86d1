import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { EachAchievementItem } from "@/utils/interfaces"
import { format } from "date-fns"
import { Pencil<PERSON><PERSON>, Trash2 } from "lucide-react"
import { Separator } from "../ui/separator"
import AlertPopup from "./AlertPopup"
import FilePreview from "./FilePreview"


interface IProps {
    item: EachAchievementItem
    handleEdit: (id: number) => void
    handleDelete: (id: number) => void
}

const AchievementItem = ({ item, handleEdit, handleDelete }: IProps) => {
    return (
        <Card className="rounded-lg p-5 gap-3 hover:shadow-lg border hover:border-slate-300">
            <CardHeader className="p-0">
                <CardTitle className="text-lg">{item?.title}</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-3 items-center p-0">
                <div className="md:col-span-2">
                    <span className="font-semibold">{item?.date && format(new Date(item?.date), 'MM-dd-yyyy')}</span>
                    <p>{item?.blurb}</p>
                    <p className="text-secondary font-semibold mt-1 capitalize flex-wrap text-wrap">
                        {item?.tags?.map((each) => each?.label)?.join(", ")}
                    </p>
                    <a
                        href={item?.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-full md:w-auto break-words underline text-center text-wrap font-semibold text-sm hover:text-blue-600 hover:underline"
                    >
                        {item?.link}
                    </a>
                </div>

                <div className="flex items-center justify-center md:justify-end">
                    <FilePreview s3FileLink={item?.file!} />
                </div>
            </CardContent>

            <Separator />

            <CardFooter className="flex items-center justify-between p-0 w-full">
                <Button size={'icon'} variant={'outline'} onClick={() => handleEdit(item?.id)}>
                    <PencilLine />
                </Button>
                <AlertPopup
                    trigger={<Button variant={'destructive'} size={'icon'}
                    >
                        <Trash2 />
                    </Button>
                    }
                    alertTitle="Confirm Deletion"
                    alertContent="Are you sure, you want to delete?"
                    action={() => handleDelete(item?.id)}
                />
            </CardFooter>
        </Card >
    )
}
export default AchievementItem