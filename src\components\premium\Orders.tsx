'use client'
import {
    <PERSON><PERSON><PERSON>,
    TooltipContent,
    <PERSON><PERSON><PERSON><PERSON><PERSON>ider,
    TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTokenValues } from "@/hooks/useTokenValues";
import { AppDispatch, RootState } from "@/store";
import { fetchTransactionsHistory } from "@/store/slices/premiumSlice";
import { ROLES } from "@/utils/constants";
import { EachTransaction } from "@/utils/interfaces";
import { format } from "date-fns";
import { Calendar, MoveLeft, MoveRight, RefreshCcw } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import CommonCalender from "../common/CommonCalender";
import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { Skeleton } from "../ui/skeleton";
import OrderSummaryItem from "./OrderSummaryItem";

function downloadTransactionAsExcel(transaction: EachTransaction) {
    // ... your existing download function
    const excludedKeys = new Set([
        "createdAt",
        "updatedAt",
        "deletedAt",
        "roleId",
        "createdUser",
        "updatedUser",
        "subscriptionTransactionId",
        "renewalWindowStartDate",
        "isLatest",
        "subscriptionPlanDuration",
        "renewalWindow",
        "billingStateId",
        "userId",
        "profileId",
        "subscriptionPlanId",
    ]);

    const filteredEntries = Object.entries(transaction)?.filter(([key, value]) => {
        if (excludedKeys?.has(key)) {
            return false;
        }
        if (
            (
                key === "processingFees" ||
                key === "convenienceFees" ||
                key === "serviceFees" ||
                key === 'discountAmount' ||
                key === 'netPlanAmount'
            ) &&
            value === 0
        ) return false;
        if ((key === 'promoCode' || key === 'discountPercentage') && value === null) return false;

        return true;
    });

    const csvHeaders = filteredEntries?.map(([key]) => key)?.join(",");
    const csvValues = filteredEntries?.map(([_, value]) => {
        if (typeof value === "string") {
            return `"${value?.replace(/"/g, '""')}"`;
        }
        return String(value);
    })?.join(",");

    const csvContent = `data:text/csv;charset=utf-8,${csvHeaders}\n${csvValues}`;
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `transaction_${transaction?.paymentDate}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

const Orders = () => {
    const { transactionsList, apiStatus } = useSelector((state: RootState) => state.premium);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [sortDate, setSortDate] = useState<Date | undefined>(undefined);
    const dispatch = useDispatch<AppDispatch>();
    const { isPremiumUser, roleId } = useTokenValues()
    const router = useRouter()

    const handleFetchTransactions = async () => {
        await dispatch(fetchTransactionsHistory())
    };

    useEffect(() => {
        handleFetchTransactions()
    }, [dispatch]);

    // For latest transaction first reversing list
    const reversedList = useMemo(
        () => [...(transactionsList || [])]?.reverse(),
        [transactionsList]
    );

    // Filter transactions if sortDate is selected
    const filteredList = useMemo(() => {
        if (!sortDate) return reversedList;
        return reversedList?.filter((t) => {
            const transactionDate = new Date(t?.paymentDate);
            const tDateStr = format(transactionDate, "yyyy-MM-dd");
            const sDateStr = format(sortDate, "yyyy-MM-dd");
            return tDateStr === sDateStr;
        });
    }, [reversedList, sortDate]);

    // When sortDate changes, reset index
    useEffect(() => {
        setCurrentIndex(0);
    }, [sortDate]);

    const currentTransaction = filteredList?.[currentIndex];

    const handleNext = () => {
        if (currentIndex < filteredList?.length - 1) {
            setCurrentIndex(prev => prev + 1);
        }
    };

    const handleClearSortDate = () => {
        setSortDate(undefined)
        setCurrentIndex(0)
    }

    if (apiStatus === 'fetchTransactionLoading') {
        return (
            <div className="w-full space-y-10 p-4 bg-white rounded-xl overflow-hidden shadow-md">
                <div className="w-full space-x-4 flex items-center">
                    <Skeleton className="w-1/2 bg-slate-200 h-8" />
                    <Skeleton className="w-1/2 bg-slate-200 h-8" />
                </div>

                <div className="w-full space-x-4 flex items-center justify-end">
                    <Skeleton className="w-1/2 bg-slate-200 h-8" />
                </div>

                <div className="space-y-2">
                    {[...Array(5)].map((_, i) => (
                        <Skeleton key={i} className="h-6 w-full bg-slate-200" />
                    ))}
                </div>
            </div>
        );
    }

    if (apiStatus === 'fetchTransactionFailed') {
        return (
            <div className="flex flex-col items-center justify-center gap-4 py-10 text-center text-muted-foreground">
                <div className="text-5xl">⚠️</div>
                <h3 className="text-xl font-semibold">We couldn’t load your reports</h3>
                <p className="max-w-md text-sm">
                    Something went wrong while fetching your data. Please check your connection and try again.
                </p>
                <Button onClick={handleFetchTransactions}>Try Again</Button>
            </div>
        );
    }

    if (transactionsList!?.length <= 0 || !isPremiumUser) {
        return (
            <div className="flex flex-col items-center justify-center gap-4 py-10 text-center text-muted-foreground">
                <div className="text-5xl">🧾</div>
                <h3 className="text-xl font-semibold">No Transactions Found</h3>
                <p className="max-w-md text-sm">
                    You haven't subscribed to any plan yet. Once you purchase a subscription, your transaction history will appear here.
                </p>
                <Link href={roleId === ROLES.ATHLETE ? `/athlete/premium-plan` : `/business/premium-plan`}>
                    <Button>Upgrade to Premium</Button>
                </Link>
            </div>
        )
    }

    return (
        <div className="flex flex-col gap-4">
            <div className="grid grid-cols-1 gap-6">
                <Card className="p-6 flex flex-col items-center justify-center gap-10">
                    {/* Payment Date and sort by */}
                    <div className="w-full flex items-center justify-between gap-3 flex-wrap">
                        <div className="flex items-center gap-3">
                            <div className="border border-primary p-[5px] rounded-full flex items-center justify-center">
                                <Calendar className="h-4 w-4" />
                            </div>
                            {currentTransaction?.paymentDate && format(new Date(currentTransaction.paymentDate), 'MMM dd, yyyy')}
                        </div>

                        <div className="flex items-center gap-3">
                            <p className="w-full text-nowrap">Sort By</p>
                            <CommonCalender
                                dateValue={sortDate}
                                setDateFn={setSortDate}
                                mode="single"
                                name="sortPaymentDate"
                                placeholder="Payment Date"
                            />
                            {sortDate &&
                                <Button onClick={handleClearSortDate} className="w-full" size={'icon'} variant={'ghost'}>
                                    <RefreshCcw />
                                </Button>}
                        </div>
                    </div>

                    {filteredList?.length > 0 ? (
                        <div className="flex flex-col gap-10 w-full">
                            {/* Excel Download */}
                            <div className="flex justify-end">
                                <button onClick={() => downloadTransactionAsExcel(currentTransaction as EachTransaction)} className="bg-transparent border-none outline-none">
                                    <div className="flex flex-col items-center gap-3">
                                        <img src='/excel-sheet.webp' alt='Excel sheet' className="h-8 w-8" />
                                        <span className="text-sm">Download <br /> to excel</span>
                                    </div>
                                </button>
                            </div>

                            {/* Order details */}
                            <OrderSummaryItem item={currentTransaction as EachTransaction} key={currentTransaction?.id} />

                            {/* Navigation buttons */}
                            <div className={`flex items-center ${currentIndex > 0 ? 'justify-between' : 'justify-end'} w-full`}>
                                {currentIndex > 0 && (
                                    <Button
                                        onClick={handleClearSortDate}
                                        variant={'ghost'}
                                        className="text-primary hover:text-primary bg-transparent hover:bg-transparent hover:shadow-md">
                                        Recent
                                    </Button>
                                )}
                                <div className="flex items-center gap-5">
                                    <TooltipProvider>
                                        {currentIndex !== 0 && (
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <Button
                                                        size="icon"
                                                        className="rounded-full"
                                                        onClick={() => setCurrentIndex(prev => prev - 1)}>
                                                        <MoveLeft />
                                                    </Button>
                                                </TooltipTrigger>
                                                <TooltipContent><p>Back</p></TooltipContent>
                                            </Tooltip>
                                        )}
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <Button
                                                    size="icon"
                                                    className="rounded-full"
                                                    onClick={handleNext}
                                                    disabled={currentIndex >= filteredList.length - 1}>
                                                    <MoveRight />
                                                </Button>
                                            </TooltipTrigger>
                                            <TooltipContent><p>Previous Orders</p></TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="flex flex-col items-center justify-center gap-4 py-10 text-center text-muted-foreground">
                            <div className="text-5xl">🧾</div>
                            <h3 className="text-xl font-semibold">No Transactions Found</h3>
                            <p className="max-w-md text-sm">
                                No transactions were found for {sortDate! && format(new Date(sortDate), 'MMM dd, yyyy')}.
                            </p>
                            <Button onClick={() => setSortDate(undefined)}>Back</Button>
                        </div>
                    )}
                </Card>
            </div>
        </div>
    );
};

export default Orders;
