import axios from "axios";
import NextAuth from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      authorize: async (credentials?: Record<string, string>) => {
        try {
          const response = await axios.post(
            `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/login`,
            {
              email: credentials?.email,
              password: credentials?.password,
            }
          );
          const user = response.data;
          if (user) {
            return {
              token: user.token,
              email: user?.user?.email,
              userFirstName: user?.user?.firstName,
              userLastName: user?.user?.lastName,
              id: user.user?.id,
              roleId: user.user?.roleId,
              profileData: user.profileData,
              galleryData: user.galleryData,
            };
          } else {
            return null;
          }
        } catch (error) {
          console.error("Login Error:", error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.token = user.token;
        token.id = user.id;
        token.email = user.email;
        token.userFirstName = user.userFirstName;
        token.userLastName = user.userLastName;
        token.roleId = user.roleId;
        token.profileData = user.profileData;
        token.galleryData = user.galleryData;
      }
      return token;
    },
    async session({ session, token }) {
      session.user.userLastName = token?.userLastName;
      session.user.userFirstName = token?.userFirstName;
      session.user.email = token?.email;
      session.user.token = token.token;
      session.user.id = token.id;
      session.user.roleId = token.roleId;
      session.user.profileData = token.profileData;
      session.user.galleryData = token.galleryData;
      return session;
    },
  },
  session: {
    strategy: "jwt", // Required for credentials provider
  },
  pages: {
    error: "/auth/error",
  },
});

export { handler as GET, handler as POST };
