"use client";

import Chip from "@/components/common/Chip";
import MultiSelectWithChip from "@/components/common/MultiSelectWithChip";
import ProfileImgUploader from "@/components/common/ProfileImgUploader";
import InstagramLink from "@/components/common/InstagramLink";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { RootState } from "@/store";
import { editCoachProfile, handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice";
import { agesList, genderList } from "@/store/slices/commonSlice";
import { zodResolver } from "@hookform/resolvers/zod";
import { PencilLine } from "lucide-react";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { z } from "zod";
import type { AppDispatch } from "@/store";

const coachProfileSchema = z.object({
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().nullable().optional(),
    blurb: z.string().nullable().optional(),
    bio: z.string().nullable().optional(),
    achievements: z.string().nullable().optional(),
    lookingFor: z.string().nullable().optional(),
    genderYouCoach: z.string().nullable().optional(),
    virtualTraining: z.string().nullable().optional(),
    websiteLink: z.string().nullable().optional(),
    verifiedStatus: z.string().nullable().optional(),
    eliteStatus: z.string().nullable().optional(),
    ageGroups: z.array(z.object({
        value: z.union([z.string(), z.number()]),
        label: z.string()
    })).optional().default([]),
    genders: z.array(z.object({
        value: z.union([z.string(), z.number()]),
        label: z.string()
    })).optional().default([]),
    profileImage: z.any().optional(),
});

type FormData = z.infer<typeof coachProfileSchema>;

const CoachAboutCard = () => {
    const dispatch = useDispatch<AppDispatch>();
    const { isProfileEditable, coachSocialMediaList } = useSelector((state: RootState) => state.coachProfile);
    const { coachProfileData } = useSelector((state: RootState) => state.coachProfile);

    const [showFull, setShowFull] = useState(false);

    const {
        register,
        control,
        handleSubmit,
        setValue,
        formState: { errors },
        watch,
        reset,
    } = useForm<FormData>({
        resolver: zodResolver(coachProfileSchema),
        defaultValues: {
            firstName: "",
            lastName: null,
            blurb: null,
            bio: null,
            achievements: null,
            lookingFor: null,
            genderYouCoach: null,
            virtualTraining: null,
            websiteLink: null,
            verifiedStatus: null,
            eliteStatus: null,
            ageGroups: [],
            genders: [],
            profileImage: undefined,
        },
    });

    // Transform API data to form format
    const transformApiDataToForm = (apiData: any): FormData => {
        if (!apiData) return {} as FormData;

        // Transform gender data
        const genderData = apiData.genderYouCoach ?
            genderList.filter(gender => gender.label === apiData.genderYouCoach) : [];

        // Transform age group data
        const ageGroupData = apiData.AgeGroup?.ageGroup?.range ?
            agesList.filter(age => age.label === apiData.AgeGroup.ageGroup.range) : [];

        return {
            firstName: apiData.firstName || "",
            lastName: apiData.lastName,
            blurb: apiData.blurb,
            bio: apiData.bio,
            achievements: apiData.achievements,
            lookingFor: apiData.lookingFor,
            genderYouCoach: apiData.genderYouCoach,
            virtualTraining: apiData.virtualTraining,
            websiteLink: apiData.websiteLink,
            verifiedStatus: apiData.verifiedStatus,
            eliteStatus: apiData.eliteStatus,
            ageGroups: ageGroupData,
            genders: genderData,
            profileImage: apiData.user?.profileImg,
        };
    };

    // Update form when API data changes
    useEffect(() => {
        if (coachProfileData) {
            const formData = transformApiDataToForm(coachProfileData);
            reset(formData);
        }
    }, [coachProfileData, reset]);

    const onSubmit = async (data: FormData) => {

        // Transform form data back to API format
        const apiPayload = {
            id: coachProfileData?.id,
            firstName: data.firstName,
            lastName: data.lastName,
            blurb: data.blurb,
            bio: data.bio,
            achievements: data.achievements,
            lookingFor: data.lookingFor,
            genderYouCoach: data.genders?.[0]?.label || null,
            virtualTraining: data.virtualTraining,
            websiteLink: data.websiteLink,
            profileImg: data.profileImage,
            // Add other fields as needed
        };

        try {
            const resultAction = await dispatch(editCoachProfile(apiPayload));
            if (editCoachProfile.fulfilled.match(resultAction)) {
                // Success - toggle edit mode off
                handleEditClick();
            }
        } catch (error) {
            console.error("Failed to update profile:", error);
        }
    };

    const handleEditClick = () => {
        dispatch(handleCoachInputChange({
            name: "isProfileEditable",
            value: !isProfileEditable
        }));
    };


    const handleClickCancel = () => {
        reset()
        handleEditClick()
    }


    const blurbContent = watch("blurb") || "";
    const isLong = blurbContent.length > 100;
    const preview = blurbContent.slice(0, 100);

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="bg-slate-100 rounded-lg p-5 flex flex-col gap-5">
            <div className="grid grid-cols-1 lg:grid-cols-3 justify-center items-center gap-3">
                <div className="col-span-1 flex flex-col gap-3">
                    <ProfileImgUploader
                        initialUrl={watch("profileImage") || '/user.svg'}
                        onChange={(file) => setValue("profileImage", file)}
                    />

                    {watch("verifiedStatus") === "Y" ?
                        <p className="text-md font-bold text-center text-green-600">✓ Verified</p>
                        :
                        <div className="flex items-center justify-center">
                            <Button className="w-24 text-xs">Verify</Button>
                        </div>
                    }

                    {watch("eliteStatus") === "Y" && (
                        <p className="text-md font-bold text-center text-purple-600">⭐ Elite Coach</p>
                    )}

                    {watch("virtualTraining") === "Yes" && (
                        <p className="text-sm text-center text-blue-600">🌐 Virtual Training Available</p>
                    )}

                    {/* Instagram Link Display */}
                    <div className="mt-2">
                        <InstagramLink
                            socialMediaList={coachSocialMediaList}
                            showIcon={true}
                            showLabel={false}
                            className="justify-center text-sm"
                        />
                    </div>
                </div>

                <div className="col-span-2 flex flex-col gap-6">
                    <div className="flex flex-col md:flex-row justify-center md:justify-right items-center gap-3">
                        {/* {isProfileEditable ? (
                            <div className="flex items-center flex-wrap gap-3 w-full">
                                <Input {...register("firstName")} placeholder="First Name" />
                                {errors.firstName && (
                                    <p className="text-sm text-red-500">{errors.firstName.message}</p>
                                )}
                                <Input {...register("lastName")} placeholder="Last Name" />
                                {errors.lastName && (
                                    <p className="text-sm text-red-500">{errors.lastName.message}</p>
                                )}
                            </div>
                        ) : (
                            <div className="flex items-center gap-3">
                                <p className="font-semibold">{watch("firstName")}</p>
                                <p className="font-semibold">{watch("lastName")}</p>
                            </div>
                        )} */}

                        <Button type="button" variant="outline" size="icon" onClick={handleEditClick}>
                            <PencilLine className="h-14" />
                        </Button>
                    </div>

                    {isProfileEditable ? (
                        <div className="w-full space-y-4">
                            <div>
                                <Label>Blurb</Label>
                                <Textarea
                                    placeholder="Write your blurb"
                                    {...register("blurb")}
                                    maxLength={200}
                                    rows={3}
                                    className="bg-white"
                                />
                                {errors.blurb && (
                                    <p className="text-sm text-red-500">{errors.blurb.message}</p>
                                )}
                            </div>

                            <div>
                                <Label>Bio</Label>
                                <Textarea
                                    placeholder="Write your bio"
                                    {...register("bio")}
                                    rows={4}
                                    className="bg-white"
                                />
                                {errors.bio && (
                                    <p className="text-sm text-red-500">{errors.bio.message}</p>
                                )}
                            </div>

                            <div>
                                <Label>Achievements</Label>
                                <Textarea
                                    placeholder="Describe your achievements"
                                    {...register("achievements")}
                                    rows={3}
                                    className="bg-white"
                                />
                                {errors.achievements && (
                                    <p className="text-sm text-red-500">{errors.achievements.message}</p>
                                )}
                            </div>

                            <div>
                                <Label>What I'm Looking For</Label>
                                <Textarea
                                    placeholder="What are you looking for?"
                                    {...register("lookingFor")}
                                    rows={3}
                                    className="bg-white"
                                />
                                {errors.lookingFor && (
                                    <p className="text-sm text-red-500">{errors.lookingFor.message}</p>
                                )}
                            </div>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {blurbContent && (
                                <div className="bg-slate-200 p-4 rounded-lg">
                                    <Label className="font-semibold">Blurb</Label>
                                    <div className="text-gray-800 break-words mt-2">
                                        {isLong && !showFull ? `${preview}...` : blurbContent}
                                    </div>
                                    {isLong && (
                                        <button
                                            onClick={() => setShowFull(!showFull)}
                                            className="text-blue-500 mt-1 hover:underline text-sm"
                                            type="button"
                                        >
                                            {showFull ? "Show less" : "Read more..."}
                                        </button>
                                    )}
                                </div>
                            )}

                            {watch("bio") && (
                                <div className="bg-slate-200 p-4 rounded-lg">
                                    <Label className="font-semibold">Bio</Label>
                                    <div className="text-gray-800 break-words mt-2">{watch("bio")}</div>
                                </div>
                            )}

                            {watch("achievements") && (
                                <div className="bg-slate-200 p-4 rounded-lg">
                                    <Label className="font-semibold">Achievements</Label>
                                    <div className="text-gray-800 break-words mt-2">{watch("achievements")}</div>
                                </div>
                            )}

                            {watch("lookingFor") && (
                                <div className="bg-slate-200 p-4 rounded-lg">
                                    <Label className="font-semibold">What I'm Looking For</Label>
                                    <div className="text-gray-800 break-words mt-2">{watch("lookingFor")}</div>
                                </div>
                            )}

                            {watch("websiteLink") && (
                                <div className="bg-slate-200 p-4 rounded-lg">
                                    <Label className="font-semibold">Website</Label>
                                    <div className="text-gray-800 break-words mt-2">
                                        <a href={watch("websiteLink") || "#"} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                                            {watch("websiteLink")}
                                        </a>
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <div className='flex flex-col gap-3'>
                    <Label>Genders I Train/Coach</Label>
                    <div>
                        {isProfileEditable ? <MultiSelectWithChip
                            name="genders"
                            control={control}
                            options={genderList}
                            placeholder="Select Genders"
                            errors={errors}
                        />
                            :
                            <div className="flex flex-wrap gap-2">
                                {(watch("genders") || []).map((gender: { label: string; value: any }) => (
                                    <Chip key={gender.value} id={gender.value} label={gender.label} />
                                ))}
                            </div>
                        }
                    </div>
                </div>

                <div className='flex flex-col gap-3'>
                    <Label>Age Groups I Train/Coach</Label>
                    <div>
                        {isProfileEditable ?
                            <MultiSelectWithChip
                                name="ageGroups"
                                control={control}
                                options={agesList}
                                placeholder="Select Age Groups"
                                errors={errors}
                            /> :
                            <div className="flex flex-wrap gap-2">
                                {(watch("ageGroups") || []).map((age: { label: string; value: any }) => (
                                    <Chip key={age.value} id={age.value} label={age.label} />
                                ))}
                            </div>
                        }
                    </div>
                </div>
            </div>

            {isProfileEditable ? <div className="flex justify-end gap-5">
                <Button variant={'outline'} onClick={handleClickCancel}>Cancel</Button>
                <Button type="submit">Save</Button>
            </div> : null}
        </form>
    );
};

export default CoachAboutCard;
