import BusinessTab from "@/components/athleteHome/BusinessTab";
import SpecialsTab from "@/components/athleteHome/SpecialsTab";
import {
    Tabs,
    TabsContent,
    TabsList,
    TabsTrigger,
} from "@/components/ui/tabs";

const tabsList = [
    {
        name: "Business",
        icon: "🏢",
        bgColor: "bg-blue-100",
        textColor: "text-blue-800",
        hoverColor: "hover:bg-blue-200",
    },
    {
        name: "Coaches",
        icon: "🎯",
        bgColor: "bg-green-100",
        textColor: "text-green-800",
        hoverColor: "hover:bg-green-200",
    },
    {
        name: "Special Offers",
        icon: "🎁",
        bgColor: "bg-yellow-100",
        textColor: "text-yellow-800",
        hoverColor: "hover:bg-yellow-200",
    },
    {
        name: "Resources",
        icon: "📚",
        bgColor: "bg-purple-100",
        textColor: "text-purple-800",
        hoverColor: "hover:bg-purple-200",
    },
];

const posts = [
    {
        id: 1,
        author: "<PERSON>",
        role: "Product Designer at Designify",
        content: "Excited to share our latest UI kit!",
        time: "2h",
        avatar: "/user.svg",
        date: "June 5, 2025"
    },
    {
        id: 2,
        author: "Mark Lee",
        role: "Software Engineer at DevCorp",
        content: "Check out my new blog on microservices.",
        time: "5h",
        avatar: "/user.svg",
        date: "June 5, 2025"
    },
    {
        id: 3,
        author: "Mark Lee",
        role: "Software Engineer at DevCorp",
        content: "Check out my new blog on microservices.",
        time: "5h",
        avatar: "/user.svg",
        date: "June 5, 2025"
    },
    {
        id: 4,
        author: "Mark Lee",
        role: "Software Engineer at DevCorp",
        content: "Check out my new blog on microservices.",
        time: "5h",
        avatar: "/user.svg",
        date: "June 5, 2025"
    }
]

const AthleteHome = () => {
    return (
        <>
            <div className="bg-white rounded-lg shadowmin-h-screen p-4 flex flex-col w-full gap-4">
                <div className="flex flex-col">
                    <h2 className="text-lg font-semibold text-gray-800">
                        🔎 Your Search Assistant
                    </h2>
                    <p className="text-sm text-gray-600">
                        Find offers, Events & Tools For your success
                    </p>
                </div>

                <Tabs
                    defaultValue={tabsList?.at(0)?.name!}
                    className="flex flex-col gap-5 w-full h-full"
                >
                    <TabsList
                        className="bg-transparent h-full w-full"
                    >
                        <div className="flex items-center justify-between gap-2 overflow-x-auto w-full h-full">
                            {tabsList?.map(({ name, icon, bgColor, textColor, hoverColor }) => (
                                <TabsTrigger
                                    value={name}
                                    key={name}
                                    className={`
                                ${bgColor} ${textColor} ${hoverColor}
                                rounded-lg shadow-md cursor-pointer
                                flex flex-col gap-3 justify-center items-center
                                p-4 min-h-[100px]
                                min-w-[120px]
                                flex-shrink-0
                                transition text-center
                                `}
                                >
                                    <div className="flex flex-col gap-2">
                                        <span className="text-3xl mb-2">{icon}</span>
                                        <span className="text-sm font-bold">{name}</span>
                                    </div>
                                </TabsTrigger>
                            ))}
                        </div>
                    </TabsList>

                    <TabsContent value={tabsList?.at(0)?.name!}>
                        <BusinessTab />
                    </TabsContent>
                    <TabsContent value={tabsList?.at(1)?.name!}>Coaches</TabsContent>
                    <TabsContent value={tabsList?.at(2)?.name!}>
                        <SpecialsTab />
                    </TabsContent>
                    <TabsContent value={tabsList?.at(3)?.name!}>Resources</TabsContent>
                </Tabs>
            </div>
        </>
    )
}
export default AthleteHome