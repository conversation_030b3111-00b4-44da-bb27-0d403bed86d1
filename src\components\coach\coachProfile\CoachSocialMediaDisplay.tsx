'use client'
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import InstagramLink from "@/components/common/InstagramLink";
import { getInstagramLink, getVisibleSocialMediaLinks } from "@/utils/socialMediaHelpers";

const CoachSocialMediaDisplay = () => {
  const { coachSocialMediaList } = useSelector((state: RootState) => state.coachProfile);

  const instagramLink = getInstagramLink(coachSocialMediaList);
  const visibleSocialMediaLinks = getVisibleSocialMediaLinks(coachSocialMediaList);

  return (
    <div className="w-full flex flex-col gap-4 bg-white p-4 rounded-lg shadow-sm">
      <h3 className="font-bold text-lg">Social Media Links</h3>
      
      {/* Display Instagram link specifically */}
      {instagramLink && (
        <div className="border-l-4 border-pink-500 pl-4">
          <h4 className="text-sm font-medium text-gray-600 mb-2">Instagram</h4>
          <InstagramLink 
            socialMediaList={coachSocialMediaList} 
            showIcon={true}
            showLabel={false}
            className="text-lg"
          />
        </div>
      )}

      {/* Display all visible social media links */}
      {visibleSocialMediaLinks.length > 0 && (
        <div className="mt-4">
          <h4 className="text-sm font-medium text-gray-600 mb-2">All Social Media</h4>
          <div className="flex flex-col gap-2">
            {visibleSocialMediaLinks.map((item) => (
              <div key={item.id} className="flex items-center gap-3">
                <img src={item.icon} alt={item.id} className="h-6 w-6" />
                <a
                  href={item.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 hover:underline truncate"
                  title={item.link}
                >
                  {item.link}
                </a>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Show message if no social media links */}
      {visibleSocialMediaLinks.length === 0 && (
        <p className="text-gray-500 text-sm">No social media links available</p>
      )}
    </div>
  );
};

export default CoachSocialMediaDisplay;
