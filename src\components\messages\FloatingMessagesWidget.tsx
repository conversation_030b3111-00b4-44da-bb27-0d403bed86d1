"use client";

import { MessageCircle, Minus, X, ExternalLink } from "lucide-react";
import { useState, useEffect } from "react";
import { AnimatePresence, motion } from "framer-motion";
import Link from "next/link";
import Avatar from "../common/Avatar";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { fetchMessagesData, ConnectedUser } from "@/store/slices/messages/messagesSlice";

const FloatingMessagesWidget = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedUser, setSelectedUser] = useState<ConnectedUser | null>(null);
  const [newMessage, setNewMessage] = useState("");
  
  const dispatch = useDispatch<AppDispatch>();
  const { connectedUsers, loading, error } = useSelector((state: RootState) => state.messages);

  // Fetch messages data on component mount
  useEffect(() => {
    dispatch(fetchMessagesData());
  }, [dispatch]);

  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded);
    if (!isExpanded) {
      setSelectedUser(null);
    }
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // ESC to close widget
      if (event.key === 'Escape' && isExpanded) {
        if (selectedUser) {
          setSelectedUser(null);
        } else {
          setIsExpanded(false);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isExpanded, selectedUser]);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (newMessage.trim() && selectedUser) {
      // TODO: Implement actual message sending with socket.io
      console.log(`Sending message to ${selectedUser.firstName}: ${newMessage}`);
      setNewMessage("");
    }
  };

  const handleSelectUser = (user: ConnectedUser) => {
    setSelectedUser(user);
  };

  const handleBackToList = () => {
    setSelectedUser(null);
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return "";
    }
  };

  const getDisplayName = (user: ConnectedUser) => {
    const firstName = user.firstName || "";
    const lastName = user.lastName || "";
    return `${firstName} ${lastName}`.trim() || "Unknown User";
  };

  const getInitials = (user: ConnectedUser) => {
    const firstName = user.firstName || "";
    const lastName = user.lastName || "";
    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase() || "U";
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ duration: 0.2 }}
            className="mb-4 bg-white rounded-lg shadow-xl border border-gray-200 w-80 h-[500px] flex flex-col overflow-hidden"
          >
            {/* Header */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {selectedUser ? getDisplayName(selectedUser) : "Messages"}
                  </h3>
                  {!selectedUser && connectedUsers.length > 0 && (
                    <p className="text-xs text-gray-500">
                      {connectedUsers.length} connected user{connectedUsers.length !== 1 ? 's' : ''}
                    </p>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  {selectedUser && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleBackToList}
                      className="p-1 h-8 w-8"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsExpanded(false)}
                    className="p-1 h-8 w-8"
                  >
                    <Minus className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 flex flex-col overflow-hidden">
              {selectedUser ? (
                /* Chat View */
                <div className="flex flex-col h-full">
                  {/* Messages */}
                  <div className="flex-1 overflow-y-auto p-3 space-y-3">
                    <div className="text-center text-gray-500 text-sm py-4">
                      Start a conversation with {getDisplayName(selectedUser)}
                    </div>
                  </div>

                  {/* Message Input */}
                  <div className="p-3 border-t border-gray-200 bg-gray-50">
                    <form onSubmit={handleSendMessage} className="flex space-x-2">
                      <Input
                        type="text"
                        placeholder="Type a message..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        className="flex-1 text-sm"
                      />
                      <Button
                        type="submit"
                        size="sm"
                        disabled={!newMessage.trim()}
                        className="px-3"
                      >
                        Send
                      </Button>
                    </form>
                  </div>
                </div>
              ) : (
                /* Conversations List */
                <div className="flex-1 overflow-y-auto">
                  {loading ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-gray-500 text-sm">Loading...</div>
                    </div>
                  ) : error ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-red-500 text-sm">Error loading messages</div>
                    </div>
                  ) : connectedUsers.length === 0 ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-gray-500 text-sm">No connected users</div>
                    </div>
                  ) : (
                    connectedUsers.map((user) => (
                      <div
                        key={user.id}
                        onClick={() => handleSelectUser(user)}
                        className="p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="relative">
                            <Avatar
                              profileImg={user.galleries?.[0]?.fileLocation || ""}
                              name={getInitials(user)}
                              styles="h-10 w-10 bg-slate-700 text-white"
                            />
                            <div className="absolute bottom-0 right-0 w-2.5 h-2.5 bg-green-500 border border-white rounded-full"></div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium text-gray-900 truncate">
                                {getDisplayName(user)}
                              </p>
                              {user.lastMessage && (
                                <p className="text-xs text-gray-400">
                                  {formatDate(user.lastMessage.createdAt)}
                                </p>
                              )}
                            </div>
                            <div className="flex flex-col">
                              <p className="text-xs text-gray-500 truncate">
                                {user.email}
                              </p>
                              {user.lastMessage && (
                                <p className="text-xs text-gray-400 truncate mt-1">
                                  {user.lastMessage.content}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  )}

                  {/* View All Messages Link - Fixed at bottom */}
                  {connectedUsers.length > 0 && (
                    <div className="p-3 border-t border-gray-200 bg-gray-50 flex-shrink-0">
                      <Link
                        href="/messages"
                        className="flex items-center justify-center space-x-2 text-sm text-blue-600 hover:text-blue-700 font-medium"
                        onClick={() => setIsExpanded(false)}
                      >
                        <span>View all messages</span>
                        <ExternalLink className="w-3 h-3" />
                      </Link>
                    </div>
                  )}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Floating Button */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={handleToggleExpanded}
        className="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-4 shadow-lg transition-colors relative"
      >
        <MessageCircle className="w-6 h-6" />
        {connectedUsers.length > 0 && (
          <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {connectedUsers.length > 9 ? '9+' : connectedUsers.length}
          </div>
        )}
      </motion.button>
    </div>
  );
};

export default FloatingMessagesWidget;
