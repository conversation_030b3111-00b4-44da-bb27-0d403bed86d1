import ClientGuard from "@/components/ClientGuard"
import BackButton from "@/components/common/BackButton"
import Payment from "@/components/premium/Payment"
import { ROLES } from "@/utils/constants"
import { Params } from "next/dist/shared/lib/router/utils/route-matcher"

const AthletePaymentPage = ({ params }: { params: Params }) => {
    const { planId } = params

    return (
        <ClientGuard allowedRoles={[ROLES.ATHLETE]}>
            <>
                <div className="flex flex-col">
                    <BackButton icon={false} className="border-none no-underline" />
                    <div className="flex flex-col gap-5">
                        <h2 className="text-primary text-2xl font-bold text-center">Payment</h2>
                        <Payment planId={planId} />
                    </div>
                </div>
            </>
        </ClientGuard>
    )
}
export default AthletePaymentPage