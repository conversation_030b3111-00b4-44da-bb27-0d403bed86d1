'use client'
import VideoUploader from "@/components/common/VideoUploader"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { AppDispatch, RootState } from "@/store"
import { fetchCoachVideoHighlight, handleCoachInputChange, postCoachVideoHighlight } from "@/store/slices/coach/coachProfileSlice"
import { zodResolver } from "@hookform/resolvers/zod"
import { Loader } from "lucide-react"
import { useEffect, useState } from "react"
import { Controller, useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { z } from "zod"

const videoHighlightSchema = z.object({
    videoTitle: z.string().min(1, "Video title is required").max(100, "Video title must be less than 100 characters"),
    abtVideo: z.string().min(1, "About video is required").max(500, "About video must be less than 500 characters"),
    videoS3Path: z.string().min(1, "Video upload is required").url("Please provide a valid video URL"),
})

type VideoHighlightFormData = z.infer<typeof videoHighlightSchema>

const LatestHighLightVideo = () => {
    const { toggleHighLightVideo, highLightVideoData, loading, coachProfileData } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch<AppDispatch>()
    const [isSubmitting, setIsSubmitting] = useState(false)

    const {
        control,
        handleSubmit,
        setValue,
        watch,
        reset,
        formState: { errors },
    } = useForm<VideoHighlightFormData>({
        resolver: zodResolver(videoHighlightSchema),
        defaultValues: {
            videoTitle: "",
            abtVideo: "",
            videoS3Path: "",
        },
    })

    const videoS3Path = watch("videoS3Path")

    // Fetch existing video highlight data on component mount
    useEffect(() => {
        dispatch(fetchCoachVideoHighlight())
    }, [dispatch])

    // Populate form with existing data
    useEffect(() => {
        if (highLightVideoData) {
            setValue("videoTitle", highLightVideoData.title || "")
            setValue("abtVideo", highLightVideoData.aboutVideo || "")
            setValue("videoS3Path", highLightVideoData.video || "")
        }
    }, [highLightVideoData, setValue])

    const handleToggleChange = (checked: boolean) => {
        dispatch(handleCoachInputChange({ name: 'toggleHighLightVideo', value: checked }))
    }

    const handleFileSelect = (url: string | null) => {
        if (url) {
            setValue("videoS3Path", url)
        }
    };

    const handleRemoveVideo = () => {
        setValue("videoS3Path", "")
    }

    const onSubmit = async (data: VideoHighlightFormData) => {
        setIsSubmitting(true)
        try {
            const userId = localStorage.getItem("userId")
            const payload = {
                roleId: 3,
                coachId: coachProfileData?.id || Number(userId || 0),
                userId: Number(userId || 0),
                videoTitle: data.videoTitle,
                abtVideo: data.abtVideo,
                videoS3Path: data.videoS3Path,
            }

            const resultAction = await dispatch(postCoachVideoHighlight(payload))

            if (postCoachVideoHighlight.fulfilled.match(resultAction)) {
                reset() // Reset form after successful submission
            }
        } catch (error) {
            console.error("Error saving video highlight:", error)
        } finally {
            setIsSubmitting(false)
        }
    }

    return (
        <>
            <div className="flex flex-col gap-10 rounded-lg bg-slate-100 p-4">
                <div className="flex items-center justify-center gap-2">
                    <h3 className="text-xl font-bold">Latest Highlight Video</h3>
                    <Switch
                        checked={toggleHighLightVideo}
                        onCheckedChange={handleToggleChange}
                    />
                </div>

                {toggleHighLightVideo ?
                    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-10">
                        <div className="flex flex-col gap-2">
                            <Label>Add Video Title *</Label>
                            <Controller
                                name="videoTitle"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        placeholder="Video Title"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.videoTitle && (
                                <p className="text-red-500 text-sm">{errors.videoTitle.message}</p>
                            )}
                        </div>

                        <div className="flex flex-col gap-2">
                            <Label>Upload Video (Max limit 1 minute) *</Label>
                            <VideoUploader
                                value={videoS3Path || null}
                                handleAdd={handleFileSelect}
                                className='w-52'
                                name={'highLightVideo'}
                                handleRemove={handleRemoveVideo}
                            />
                            {errors.videoS3Path && (
                                <p className="text-red-500 text-sm">{errors.videoS3Path.message}</p>
                            )}
                        </div>

                        <div className="flex flex-col gap-2">
                            <Label>About Video *</Label>
                            <Controller
                                name="abtVideo"
                                control={control}
                                render={({ field }) => (
                                    <Textarea
                                        placeholder="Write about video"
                                        className="bg-white"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.abtVideo && (
                                <p className="text-red-500 text-sm">{errors.abtVideo.message}</p>
                            )}
                        </div>

                        <div className="flex justify-end items-end self-end ">
                            <Button
                                type="submit"
                                className="w-28"
                                disabled={isSubmitting || loading}
                            >
                                {isSubmitting || loading ? (
                                    <>
                                        <Loader className="mr-2 h-4 w-4 animate-spin" />
                                        Saving...
                                    </>
                                ) : (
                                    "Save"
                                )}
                            </Button>
                        </div>
                    </form> : null}

            </div>

        </>
    )
}
export default LatestHighLightVideo