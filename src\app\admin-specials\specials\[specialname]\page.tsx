'use client';

import BackButton from '@/components/common/BackButton';
import { BsBuilding } from 'react-icons/bs';
import {
  FaCalendarAlt,
  FaEnvelope,
  FaGlobe,
  FaMapMarkerAlt,
  FaPhoneAlt,
  FaUser,
} from 'react-icons/fa';
import { FiTag } from 'react-icons/fi';
import { MdCategory } from 'react-icons/md';

export default function SpecialDetailsPage() {
  return (
    <div className="max-w-3xl mx-auto py-8 px-4 space-y-6">
      <BackButton />

      {/* URL Path */}
      <p className="text-center text-sm text-blue-900 underline">{'<website>/specials/<specialname>'}</p>

      {/* Banner */}
      <div className="w-full h-64 bg-black text-white flex items-center justify-center text-center text-sm">
        Banner size 640 px width x 400 px height
      </div>

      {/* Header Row */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center bg-blue-100 p-4 rounded-xl">
        <div className="font-bold text-blue-900 text-lg">&lt;Special Name&gt;</div>
        <div className="text-sm text-blue-900">&lt;From Date&gt; - &lt;To Date&gt;</div>
      </div>

      {/* Category Row */}
      <div className="flex flex-wrap gap-4 text-blue-900 text-sm">
        <div className="flex items-center gap-1"><FiTag /> &lt;Special Type&gt;</div>
        <div className="flex items-center gap-1"><MdCategory /> &lt;Special Category&gt;</div>
        <div className="flex items-center gap-1"><FaCalendarAlt /> &lt;Channel&gt;</div>
      </div>

      {/* Description and Business Info */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="bg-gray-100 text-red-500 text-center text-sm p-4 rounded-md flex-1">
          Blurb Text (max 200 chars)
        </div>
        <div className="flex-1 space-y-2 text-blue-900 text-sm">
          <div className="flex items-center gap-2"><BsBuilding /> &lt;Business Name&gt;</div>
          <div className="flex items-center gap-2"><FaGlobe /> &lt;Website&gt;</div>
          <div className="flex items-center gap-2"><FaMapMarkerAlt /> &lt;City&gt; • &lt;State&gt;</div>
        </div>
      </div>

      {/* Contact Info */}
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-blue-900 text-sm">
        <div className="flex items-center gap-2"><FaUser /> &lt;First Name&gt; &lt;Last Name&gt;</div>
        <div className="flex items-center gap-2"><FaPhoneAlt /> &lt;Phone&gt;</div>
        <div className="flex items-center gap-2"><FaEnvelope /> &lt;Email&gt;</div>
      </div>

      {/* Detail Box */}
      <div className="bg-gray-100 text-red-500 text-center text-sm p-4 rounded-md">
        Details (max 400 chars)
      </div>

      {/* Metadata */}
      <div className="text-sm text-blue-900 space-y-1">
        <p>&lt;Source URL&gt;</p>
        <p className="text-xs text-blue-700 font-medium">This opportunity is need-based</p>
        <div className="flex flex-wrap gap-4">
          <p>&lt;Deadline Date&gt;</p>
          <p>&lt;Sports&gt;</p>
          <p>&lt;Speciality&gt;</p>
          <p>&lt;Age Group&gt;</p>
        </div>
      </div>
    </div>
  );
}
