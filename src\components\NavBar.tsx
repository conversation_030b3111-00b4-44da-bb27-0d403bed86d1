import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo";
import { useTokenValues } from "@/hooks/useTokenValues";
import { ROLES } from "@/utils/constants";
import {
    ArrowDownUp,
    ChevronDown,
    LogOut,
    Menu
} from "lucide-react";
import Link from "next/link";
import { ReactNode, useEffect, useMemo, useState } from "react";
import Avatar from "./common/Avatar";
import DropdownMenusItem from "./common/DropdownMenuItem";
import NavItem from "./common/NavItem";
import { generateUpdateProfileUrl } from "@/utils/commonFunctions";

export type EachNavItem = {
    id: string,
    icon: ReactNode,
    label: string | ReactNode,
    route: string,
    labelIcon?: ReactNode,
}
export type NavListItem = {
    id: string,
    icon: ReactNode,
    label: string | ReactNode,
    route: string,
    labelIcon?: ReactNode,
    dropdownMenus?: EachNavItem[]
}

const NavBar = () => {
    const [navList, setNavList] = useState<NavListItem[]>([]);
    const { profileData } = useLocalStoredInfo()
    const { roleId } = useTokenValues()
    const profileUrl = profileData && generateUpdateProfileUrl(profileData);

    const commonMenusList = [
        {
            id: 'help',
            icon: <img src={'/helpIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Help',
            route: ''
        },
        {
            id: "logout",
            icon: <></>,
            label: "Logout",
            labelIcon: <LogOut className="h-4" />,
            route: "",
        },
    ];

    const athleteMenusList = [
        {
            id: 'athleteHome',
            icon: <img src={'/home.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Home',
            route: '/athlete'
        },
        {
            id: 'athleteResources',
            icon: <img src={'/resources.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Resources',
            route: ''
        },
        {
            id: 'athleteExplore',
            icon: <img src={'/explore.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Explore',
            labelIcon: <ChevronDown className="h-5 w-5" />,
            route: '',
            dropdownMenus: [
                {
                    id: 'exploreCoaches',
                    icon: <></>,
                    label: 'Coaches',
                    route: '/explore-coaches',
                },
                {
                    id: 'exploreSportsBusiness',
                    icon: <></>,
                    label: 'Sports Business',
                    route: '/explore-business',
                },
                {
                    id: 'exploreSpecials',
                    icon: <></>,
                    label: 'Specials',
                    route: '/admin-specials',
                },
            ],

        },
        {
            id: 'athleteSaved',
            icon: <img src={'/saveIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Saved',
            route: ''
        },
        {
            id: 'athleteMessages',
            icon: <img src={'/message.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Messages',
            route: '/athlete/messages'
        },
        // {
        //     id: 'athleteHelp',
        //     icon: <img src={'/helpIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
        //     label: 'Help',
        //     route: ''
        // },
        {
            id: 'athleteProfile',
            icon: <img src={'/profileIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Profile',
            route: profileUrl || ''
        },
        {
            id: 'athleteSettings',
            icon: <Avatar profileImg="" name={profileData?.userFirstName?.at(0)} styles="h-8 w-8 bg-primary text-white" />,
            label: "Settings",
            labelIcon: <ChevronDown className="fill-slate-700  text-slate-700" />,
            route: '',
            dropdownMenus: [
                {
                    id: "premiumPlan",
                    icon: <></>,
                    label: "Premium Plan",
                    labelIcon: <img src={'/premium-badge.svg'} className="rounded-lg h-full object-fill w-7" />,
                    route: "/athlete/premium-plan",
                },
                {
                    id: "orders",
                    icon: <></>,
                    label: "Orders",
                    labelIcon: <ArrowDownUp className="rounded-lg h-full object-fill w-7" />,
                    route: "/athlete/orders",
                }
                , ...commonMenusList]
        },
    ]

    const coachMenusList = [
        {
            id: 'coachHome',
            icon: <img src={'/home.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Home',
            route: '/coach'
        },
        {
            id: 'coachResources',
            icon: <img src={'/resources.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Resources',
            route: ''
        },
        {
            id: 'coachExplore',
            icon: <img src={'/explore.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Explore',
            route: ''
        },
        {
            id: 'coachSaved',
            icon: <img src={'/saveIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Saved',
            route: ''
        },
        {
            id: 'coachMessages',
            icon: <img src={'/message.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Messages',
            route: ''
        },
        // {
        //     id: 'coachHelp',
        //     icon: <img src={'/helpIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
        //     label: 'Help',
        //     route: ''
        // },
        {
            id: 'coachProfile',
            icon: <img src={'/profileIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Profile',
            route: profileUrl || ''
        },
        {
            id: 'coachSettings',
            icon: <Avatar profileImg="" name={profileData?.userFirstName?.at(0)} styles="h-8 w-8 bg-slate-700 text-white" />,
            label: "Settings",
            labelIcon: <ChevronDown className="fill-slate-700  text-slate-700" />,
            route: '',
            dropdownMenus: commonMenusList
        },
    ]

    const businessMenusList = [
        {
            id: 'businessHome',
            icon: <img src={'/home.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Home',
            route: '/business'
        },
        {
            id: 'businessResources',
            icon: <img src={'/resources.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Resources',
            route: ''
        },
        {
            id: 'businessExplore',
            icon: <img src={'/explore.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Explore',
            route: ''
        },
        {
            id: 'businessSaved',
            icon: <img src={'/saveIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Saved',
            route: ''
        },
        {
            id: 'businessMessages',
            icon: <img src={'/message.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Messages',
            route: ''
        },
        // {
        //     id: 'businessHelp',
        //     icon: <img src={'/helpIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
        //     label: 'Help',
        //     route: ''
        // },
        {
            id: 'businessProfile',
            icon: <img src={'/profileIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Profile',
            route: profileUrl || ''
        },
        {
            id: 'businessSettings',
            icon: <Avatar profileImg="" name={profileData?.userFirstName?.at(0)} styles="h-8 w-8 bg-slate-700 text-white" />,
            label: "Settings",
            labelIcon: <ChevronDown className="fill-slate-700  text-slate-700" />,
            route: '',
            dropdownMenus: [
                {
                    id: "premiumPlan",
                    icon: <></>,
                    label: "Premium Plan",
                    labelIcon: <img src={'/premium-badge.svg'} className="rounded-lg h-full object-fill w-7" />,
                    route: "/business/premium-plan",
                },
                {
                    id: "orders",
                    icon: <></>,
                    label: "Orders",
                    labelIcon: <ArrowDownUp className="h-4" />,
                    route: "/business/orders",
                }
                , ...commonMenusList]
        },
    ]

    const computedNavList = useMemo(() => {
        switch (roleId) {
            case ROLES.ATHLETE:
                return athleteMenusList;
            case ROLES.COACH:
                return coachMenusList;
            case ROLES.BUSINESS:
                return businessMenusList;
            default:
                return [];
        }
    }, [roleId]);

    useEffect(() => {
        computedNavList?.length && setNavList(computedNavList);
    }, [computedNavList]);

    const filterMobileMenusList = navList?.filter(each => !each?.id?.toLowerCase().includes('settings'))
    const filteredDropdownsList = navList?.flatMap(each => each?.dropdownMenus || []);

    return (
        <nav className="h-15 p-1 fixed top-0 left-0 right-0 w-full z-50  bg-slate-300">
            <div className="max-w-7xl mx-auto p-1 md:px-14 flex justify-between items-center ">
                <img src='/connectathlete-logo.svg' alt="Connect Athlete" />

                <ul className="hidden lg:flex items-center gap-10">
                    {navList?.map(each =>
                        each?.dropdownMenus ?
                            <DropdownMenusItem
                                triggerMenu={<NavItem item={each} />}
                                menusList={each?.dropdownMenus}
                                key={each?.id}
                            />
                            : 
                            (
                                <Link href={each?.route} key={each?.id} className="flex flex-col items-center">
                                    <NavItem item={each} />
                                </Link>
                            )
                    )}
                </ul>

                {/* Mobile View */}
                <div className="lg:hidden flex flex-col gap-3">
                    <DropdownMenusItem
                        triggerMenu={<Menu className="text-white" size={24} />}
                        menusList={[...filterMobileMenusList, ...filteredDropdownsList]}
                    />
                </div>
            </div>
        </nav>
    )
}
export default NavBar