import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { EachMileStoneVictoryItem } from '@/utils/interfaces';
import { format } from 'date-fns';
import { PencilLine, Trash2 } from 'lucide-react';
import AlertPopup from './AlertPopup';
import FilePreview from './FilePreview';


interface IProps {
    item: EachMileStoneVictoryItem
    handleEditMileStone: (id: number) => void
    handleDeleteMileStone: (id: number) => void
}

const MileStoneItem = ({ item, handleEditMileStone, handleDeleteMileStone }: IProps) => {

    return (
        <div className="w-full p-3">
            <div className="grid grid-cols-1 sm:grid-cols-4 flex-col sm:flex-row items-start sm:items-center w-full">
                <div className="flex items-center w-full">
                    <div
                        className="w-12 h-12 bg-primary clip-diamond shrink-0 rotate-45"
                    />
                    <div className="flex flex-col items-center w-full">
                        <span className="text-xs text-primary font-bold text-center sm:text-left">
                            {item?.date && format(new Date(item?.date), "MMM dd, yyyy")}
                        </span>
                        <div className="border-t border-dashed border-primary w-full"></div>
                    </div>
                </div>
                <Card className="w-full bg-white shadow-lg sm:col-span-3 py-1">
                    <CardContent className='p-4 flex flex-col gap-2'>
                        <div className="grid grid-cols-1 sm:grid-cols-3 justify-between items-center gap-3">
                            <div className="flex-1 sm:col-span-2">
                                <h3 className="text-lg font-semibold text-secondary">{item?.title}</h3>
                                <p className="text-sm text-gray-600">{item?.blurb}</p>
                                <p className="text-blue-700 font-semibold mt-1">
                                    {item?.tags?.map((each) => each?.label)?.join(", ")}
                                </p>
                                {item?.link && (
                                    <a
                                        href={`https://${item?.link}`}
                                        className="break-words text-wrap underline text-sm text-gray-800 hover:underline hover:text-blue-500"
                                        target="_blank"
                                        rel="noreferrer"
                                    >
                                        {item?.link}
                                    </a>
                                )}
                            </div>

                            <div className="w-full h-full rounded-md flex items-center justify-center overflow-hidden">
                                {/* {item?.file ? (
                                    item?.file?.type?.startsWith("image/") ? (
                                        <img
                                            src={URL.createObjectURL(item?.file)}
                                            alt="Milestone"
                                            className="object-fill w-full h-full max-h-56"
                                        />
                                    ) : item?.file?.type?.startsWith("video/") ? (
                                        <video controls className="w-full h-full object-fill">
                                            <source
                                                src={URL.createObjectURL(item?.file)}
                                                type={item?.file?.type}
                                            />
                                            Your browser does not support the video tag.
                                        </video>
                                    ) : item?.file?.type === "application/pdf" ? (
                                        <embed
                                            src={URL.createObjectURL(item?.file)}
                                            type="application/pdf"
                                            className="w-full h-full"
                                        />
                                    ) : (
                                        <p className="text-gray-600 text-sm px-4 text-center">
                                            Unsupported file type: {item?.file?.type}
                                        </p>
                                    )
                                ) : (
                                    <p className="text-gray-400 text-sm">No file uploaded</p>
                                )} */}
                                <FilePreview s3FileLink={item?.file}/>
                            </div>

                        </div>

                        <div className="flex items-center w-full justify-between h-full gap-2">
                            <Button variant={'outline'} size={'icon'} onClick={() => handleEditMileStone(item?.id)}>
                                <PencilLine className="w-4 h-4 text-gray-600 cursor-pointer" />
                            </Button>
                            <AlertPopup
                                trigger={<Button variant={'destructive'} size={'icon'}
                                >
                                    <Trash2 />
                                </Button>
                                }
                                alertTitle="Confirm Deletion"
                                alertContent="Are you sure, you want to delete?"
                                action={() => handleDeleteMileStone(item?.id)}
                            />
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default MileStoneItem;
