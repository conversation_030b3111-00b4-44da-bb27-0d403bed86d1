'use client'
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store';
import { fetchCoachSocialMediaLinks } from '@/store/slices/coach/coachProfileSlice';
import InstagramLink from '@/components/common/InstagramLink';
import CoachSocialMediaDisplay from '@/components/coach/coachProfile/CoachSocialMediaDisplay';
import { getInstagramLink, getSocialMediaLink, getVisibleSocialMediaLinks } from '@/utils/socialMediaHelpers';

const TestSocialMediaPage = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { coachSocialMediaList, loading } = useSelector((state: RootState) => state.coachProfile);

  useEffect(() => {
    dispatch(fetchCoachSocialMediaLinks());
  }, [dispatch]);

  const instagramLink = getInstagramLink(coachSocialMediaList);
  const xLink = getSocialMediaLink(coachSocialMediaList, 'x');
  const visibleLinks = getVisibleSocialMediaLinks(coachSocialMediaList);

  if (loading) {
    return <div className="p-8">Loading social media data...</div>;
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Social Media Test Page</h1>
      
      {/* Raw Data Display */}
      <div className="mb-8 p-4 bg-gray-100 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Raw Social Media Data</h2>
        <pre className="text-sm overflow-auto">
          {JSON.stringify(coachSocialMediaList, null, 2)}
        </pre>
      </div>

      {/* Instagram Link Component Test */}
      <div className="mb-8 p-4 bg-blue-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Instagram Link Component</h2>
        <InstagramLink 
          socialMediaList={coachSocialMediaList}
          showIcon={true}
          showLabel={true}
          className="text-lg"
        />
        {!instagramLink && (
          <p className="text-gray-500 mt-2">No Instagram link found or link is hidden</p>
        )}
      </div>

      {/* Helper Functions Test */}
      <div className="mb-8 p-4 bg-green-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Helper Functions Results</h2>
        <div className="space-y-2">
          <p><strong>Instagram Link:</strong> {instagramLink || 'Not found'}</p>
          <p><strong>X/Twitter Link:</strong> {xLink || 'Not found'}</p>
          <p><strong>Visible Links Count:</strong> {visibleLinks.length}</p>
          <div className="mt-2">
            <strong>All Visible Links:</strong>
            <ul className="list-disc list-inside mt-1">
              {visibleLinks.map((link) => (
                <li key={link.id}>
                  {link.id}: {link.link}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Full Social Media Display Component */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Full Social Media Display Component</h2>
        <CoachSocialMediaDisplay />
      </div>

      {/* API Response Example */}
      <div className="mb-8 p-4 bg-yellow-50 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Expected API Response Format</h2>
        <pre className="text-sm overflow-auto">
{`[
  {
    "id": 7,
    "roleId": 3,
    "coachId": 69,
    "userId": 220,
    "socialMedia": "Instagram",
    "socialMediaLink": "https://instagram.com/coach_profile",
    "isHidden": false,
    "createdAt": "2025-08-03T09:04:17.000Z",
    "updatedAt": "2025-08-03T09:04:17.000Z"
  },
  {
    "id": 8,
    "roleId": 3,
    "coachId": 69,
    "userId": 220,
    "socialMedia": "X",
    "socialMediaLink": "https://X.com/in/coach_profile",
    "isHidden": false,
    "createdAt": "2025-08-03T09:04:17.000Z",
    "updatedAt": "2025-08-03T09:04:17.000Z"
  }
]`}
        </pre>
      </div>
    </div>
  );
};

export default TestSocialMediaPage;
