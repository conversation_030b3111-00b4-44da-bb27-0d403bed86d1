import {
    ColumnDef,
    ColumnFiltersState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    SortingState,
    useReactTable,
} from "@tanstack/react-table";
import React from "react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ArrowDown, ArrowUp, Search } from "lucide-react";

type CommonTableProps<TData> = {
    data?: TData[];
    columns: ColumnDef<TData, any>[];
    noPagination?: boolean;
    noSorting?: boolean;
    noGlobalSearch?: boolean;
};

export function GlobalSortPaginationTable<TData>({
    data = [],
    columns,
    noPagination,
    noSorting,
    noGlobalSearch,
}: CommonTableProps<TData>) {
    const [globalFilter, setGlobalFilter] = React.useState("");
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);

    const table = useReactTable({
        data,
        columns: [...columns],
        state: {
            globalFilter,
            sorting,
            columnFilters,
        },
        onGlobalFilterChange: setGlobalFilter,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
    });

    return (
        <div className="space-y-4">
            {/* Global Search */}
            {!noGlobalSearch && data.length > 0 && (
                <div className="relative bg-white">
                    <Input
                        placeholder="Search all columns..."
                        value={globalFilter ?? ""}
                        onChange={(e) => setGlobalFilter(e.target.value)}
                        className="w-full"
                    />
                    <span className="absolute right-2 top-1/2 -translate-y-1/2">
                        <Search className="text-slate-400 w-5" />
                    </span>
                </div>
            )}

            {/* Table */}
            <div className="rounded-md border overflow-x-auto">
                <table className="min-w-xl w-full text-sm text-left">
                    <thead className="bg-slate-200">
                        {table.getHeaderGroups().map((headerGroup) => (
                            <tr key={headerGroup.id}>
                                {headerGroup.headers.map((header) => {
                                    const isSortable = header.column.getCanSort() && !noSorting;
                                    return (
                                        <th
                                            key={header.id}
                                            onClick={isSortable ? header.column.getToggleSortingHandler() : undefined}
                                            className="px-4 py-2 cursor-pointer select-none"
                                        >
                                            <div className="flex items-center justify-center min-w-[8rem] text-center gap-1">
                                                {flexRender(header.column.columnDef.header, header.getContext())}
                                                {!noSorting && (
                                                    <div className="flex items-center">
                                                        <ArrowUp className="text-slate-400 w-4" />
                                                        <ArrowDown className="text-slate-400 w-4" />
                                                    </div>
                                                )}
                                            </div>
                                        </th>
                                    );
                                })}
                            </tr>
                        ))}
                    </thead>

                    <tbody>
                        {table.getRowModel().rows.length > 0 ? (
                            table.getRowModel().rows.map((row) => (
                                <tr key={row.id} className="border-t">
                                    {row.getVisibleCells().map((cell) => (
                                        <td key={cell.id} className="px-4 py-2 text-center">
                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                        </td>
                                    ))}
                                </tr>
                            ))
                        ) : (
                            <tr>
                                <td
                                    colSpan={table.getAllColumns().length || 1}
                                    className="text-gray-500 text-center py-6"
                                >
                                    No data found — start by adding your first record.
                                </td>
                            </tr>
                        )}
                    </tbody>
                </table>
            </div>

            {/* Pagination */}
            {!noPagination && data.length > 0 && (
                <div className="flex items-center justify-between">
                    <div className="space-x-2">
                        <Button
                            className="hover:text-primary"
                            onClick={() => table.previousPage()}
                            disabled={!table.getCanPreviousPage()}
                        >
                            Previous
                        </Button>
                        <Button
                            className="hover:text-primary"
                            onClick={() => table.nextPage()}
                            disabled={!table.getCanNextPage()}
                        >
                            Next
                        </Button>
                    </div>
                    <span className="text-sm">
                        Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
                    </span>
                </div>
            )}
        </div>
    );
}
