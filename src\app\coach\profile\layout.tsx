import ClientGuard from "@/components/ClientGuard"
import SpotLights from "@/components/common/spotlight/SpotLights"

const ProfileLayout = ({ children }) => {
    return (
        <ClientGuard allowedRoles={[3]}>
            <div className="max-w-7xl  w-full mx-auto flex flex-col lg:flex-row lg:space-x-6 p-5 px-6">
                <main className="flex flex-col gap-8 lg:w-3/4">
                    {children}
                </main>

                <aside className="lg:w-1/4 mb-6 lg:mb-0">
                    <div className="sticky top-20">
                        <SpotLights />
                    </div>
                </aside>
            </div>
        </ClientGuard>
    )
}
export default ProfileLayout