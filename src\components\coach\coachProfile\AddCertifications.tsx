'use client'

import CommonCalender from "@/components/common/CommonCalender"
import SearchInput from "@/components/common/SearchInput"
import UploadFiles from "@/components/common/UploadFiles"
import { Button } from "@/components/ui/button"
import {
    <PERSON><PERSON>, DialogClose, <PERSON>alog<PERSON>ontent,
    <PERSON><PERSON><PERSON>ooter, <PERSON>alogHeader, DialogTitle, DialogTrigger
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AppDispatch, RootState } from "@/store"
import { handleCoachInputChange, postAddGovtDocument } from "@/store/slices/coach/coachProfileSlice"
import { EachSearchItem } from "@/utils/interfaces"
import { zodResolver } from '@hookform/resolvers/zod'
import { Plus } from "lucide-react"
import { useEffect } from "react"
import { Controller, useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { z } from 'zod'

// Validation schema - matching EachCertificateItem structure
const certificationSchema = z.object({
    title: z.string().min(1, "Title is required"),
    documentType: z.object({
        value: z.number(),
        label: z.string()
    }).nullable().refine(val => val !== null, "Document type is required"),
    documentLink: z.string().optional(),
    file: z.any().optional(),
    expirationDate: z.date().optional(),
    description: z.string().optional(),
})

type CertificationFormData = z.infer<typeof certificationSchema>

const AddCertifications = () => {
    const {
        isAddCertificates,
        isEditCertificate,
        certificatesData,
        coachAddedCertificatesList
    } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch<AppDispatch>()

    // Form validation setup
    const {
        handleSubmit,
        setValue,
        control,
        formState: { errors },
        reset
    } = useForm<CertificationFormData>({
        resolver: zodResolver(certificationSchema),
        defaultValues: {
            title: certificatesData?.title || '',
            documentType: certificatesData?.documentType || undefined,
            documentLink: certificatesData?.documentLink || '',
            file: certificatesData?.file || null,
            expirationDate: undefined, // This field doesn't exist in EachCertificateItem
            description: '', // This field doesn't exist in EachCertificateItem
        }
    })

    const handleOnChange = (name: string, selected: EachSearchItem | File | string | null) => {
        dispatch(handleCoachInputChange({
            name: 'certificatesData',
            value: { ...certificatesData, [name]: selected }
        }))
        // Also update form values
        setValue(name as keyof CertificationFormData, selected as any)
    }

    const handleModal = () => {
        dispatch(handleCoachInputChange({ name: 'isAddCertificates', value: !isAddCertificates }))
        if (!isAddCertificates) {
            reset() // Reset form when opening modal
        }
    }

    const onSubmit = (data: CertificationFormData) => {
        if (isEditCertificate && certificatesData?.id) {
            // Update existing
            const updatedList = coachAddedCertificatesList.map(cert =>
                cert.id === certificatesData.id
                    ? { ...certificatesData, uploadedOn: cert.uploadedOn }
                    : cert
            )

            dispatch(handleCoachInputChange({
                name: 'coachAddedCertificatesList',
                value: updatedList
            }))
        } else {
            // Add new
            const newCertificate = {
                ...certificatesData,
                uploadedOn: new Date(),
                id: `${Date.now()}-${Math.floor(Math.random() * 10000)}`,
            }

            dispatch(handleCoachInputChange({
                name: 'coachAddedCertificatesList',
                value: [...coachAddedCertificatesList, newCertificate],
            }))
        }

        // Prepare payload in the requested format
        const payload = {
            docTitle: data.title || "Coaching Certification",
            docExpirationDate: data.expirationDate ? new Date(data.expirationDate).toISOString() : "2025-12-31T00:00:00.000Z",
            docDesc: data.description || "Professional coaching certification document",
            docLink: data.documentLink || "https://example.com/certification.pdf",
            docFilePath: data.file || "/documents/certification.pdf",
            docTypeId: data.documentType?.value || 1,   // Default to 1 if not provided
            docTypTxt: data.documentType?.label || "Certification", // Default to "Certification" if not provided
            userId: Number(localStorage.getItem("userId") || 0),
            roleId: 3,
            coachId: Number(localStorage.getItem("profileId") || 0),
        }

        dispatch(handleCoachInputChange({ name: 'isAddCertificates', value: false }))
        dispatch(handleCoachInputChange({ name: 'isEditCertificate', value: false }))
        dispatch(handleCoachInputChange({ name: 'certificatesData', value: null }))

        // Send the payload to postAddGovtDocument
        dispatch(postAddGovtDocument(payload))
    }

    useEffect(() => {
        return () => {
            dispatch(handleCoachInputChange({ name: 'certificatesData', value: null }))
            dispatch(handleCoachInputChange({ name: 'isEditCertificate', value: false }))
        }
    }, [dispatch])


    return (
        <Dialog open={isAddCertificates} onOpenChange={handleModal}>
            <DialogTrigger asChild>
                <Button className="w-24">
                    <Plus /> Add
                </Button>
            </DialogTrigger>
            <DialogContent onInteractOutside={(event) => event.preventDefault()} className="w-[50vw] max-w-full max-h-[90%] p-8 flex flex-col">
                <DialogHeader>
                    <DialogTitle>
                        {isEditCertificate ? 'Edit Certification' : 'Add Certification'}
                    </DialogTitle>
                </DialogHeader>
                <form id="certification-form" onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-8 p-8 overflow-y-auto">
                    <div className="grid gap-2">
                        <Label htmlFor="title">Title</Label>
                        <Input
                            id="title"
                            name="title"
                            value={certificatesData?.title || ''}
                            placeholder="Enter your title"
                            onChange={(e) => handleOnChange('title', e.target.value)}
                        />
                        {errors.title && (
                            <p className="text-red-500 text-sm">{errors.title.message}</p>
                        )}
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="title">Description</Label>
                        <Input
                            id="description"
                            name="description"
                            value={certificatesData?.description || ''}
                            placeholder="Enter your description"
                            onChange={(e) => handleOnChange('description', e.target.value)}
                        />
                        {errors.description && (
                            <p className="text-red-500 text-sm">{errors.description.message}</p>
                        )}
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="documentType">Document Type</Label>
                        <SearchInput
                            list={[
                                { value: 1, label: 'Certification' },
                                { value: 2, label: 'Award' },
                                { value: 3, label: 'Lincence' }
                            ]}
                            value={certificatesData?.documentType}
                            name="documentType"
                            placeholder="Select Document Type"
                            onChange={(_, selected) => handleOnChange('documentType', selected)}
                        />
                        {errors.documentType && (
                            <p className="text-red-500 text-sm">{errors.documentType.message}</p>
                        )}
                    </div>

                    <div className="flex flex-col gap-2">
                        <Label>
                            Expiration Date *
                            {certificatesData?.documentType?.label === 'Certification' && " (If document has expiration date)"}
                        </Label>
                        <Controller
                            name="expirationDate"
                            control={control}
                            render={({ field }) => (
                                <CommonCalender
                                    placeholder="Pick Expiration Date"
                                    mode="single"
                                    dateValue={field.value}
                                    setDateFn={(date) => field.onChange(date)}
                                />
                            )}
                        />
                        {errors.expirationDate && (
                            <p className="text-red-500 text-sm">{errors.expirationDate.message}</p>
                        )}
                    </div>

                    <div className="grid gap-2">
                        <Label htmlFor="file">Upload PDF, Jpg, Jpeg, Mp4</Label>
                        <UploadFiles
                            value={certificatesData?.file!}
                            onFileSelect={(file) => handleOnChange('file', file)}
                            handleRemove={() => handleOnChange('file', null)}
                            acceptType={['image/png', 'image/jpg', 'image/jpeg', "application/pdf"]}
                            className="w-52"
                        />
                    </div>
                    <div className="grid gap-2">
                        <Label htmlFor="documentLink">Document Link</Label>
                        <Input
                            id="documentLink"
                            name="documentLink"
                            value={certificatesData?.documentLink || ''}
                            placeholder="Enter your Document Link"
                            onChange={(e) => handleOnChange('documentLink', e.target.value)}
                        />
                        {errors.documentLink && (
                            <p className="text-red-500 text-sm">{errors.documentLink.message}</p>
                        )}
                    </div>
                </form>
                <DialogFooter>
                    <DialogClose asChild>
                        <Button variant="outline">Cancel</Button>
                    </DialogClose>
                    <Button type="submit" form="certification-form">
                        {isEditCertificate ? 'Update' : 'Save'}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

export default AddCertifications
