import { RootState } from "@/store";
import { jwtDecode } from "jwt-decode";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";

interface DecodedToken {
  id: number;
  email: string;
  roleType: string;
  roleId: number;
  isSubscription?: string;
  iat: number;
  exp: number;
}

interface TokenValues {
  isPremiumUser: boolean | null;
  userId: number | null;
  email: string | null;
  roleType: string | null;
  roleId: number | null;
}

const defaultTokenValues: TokenValues = {
  isPremiumUser: false,
  userId: null,
  email: null,
  roleType: null,
  roleId: null,
};

export const getDecodedToken = (token: string | null) => {
  if (!token) return null;

  try {
    return jwtDecode(token);
  } catch (err) {
    return null;
  }
};

export const useTokenValues = (): TokenValues => {
  const [tokenValues, setTokenValues] =
    useState<TokenValues>(defaultTokenValues);
  const { token } = useSelector((state: RootState) => state.login);

  const updateTokenValues = () => {
    const storedToken = token || localStorage.getItem("token");

    if (!storedToken) {
      setTokenValues(defaultTokenValues);
      return;
    }

    try {
      const decoded = jwtDecode<DecodedToken>(storedToken);
      setTokenValues({
        isPremiumUser: decoded?.isSubscription === "Active",
        userId: decoded.id,
        email: decoded.email,
        roleType: decoded.roleType,
        roleId: decoded.roleId,
      });
    } catch (error) {
      console.error("Invalid token", error);
      setTokenValues(defaultTokenValues);
    }
  };

  useEffect(() => {
    updateTokenValues();
  }, [token]);

  return tokenValues;
};
