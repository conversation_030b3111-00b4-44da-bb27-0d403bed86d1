"use client";
import { useSelector, useDispatch } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import { Button } from "../ui/button";
import {
  setFormUserType,
  fetchRoleId,
} from "@/store/slices/auth/registerSlice";
import parse, { domToReact } from "html-react-parser";
import {
  titleParseOptions,
  shortDescriptionParseOptions,
  fileTitleParseOptions,
  fileDescriptionParseOptions,
} from "@/utils/parseOptions";

const BuiltForFamilies = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { builtForFamiliesSection, builtForFamiliesImages, loading } =
    useSelector((state: RootState) => state.homeCMS);

  const scrollToRegistration = async () => {
    const section = document.getElementById("registration");
    if (section) {
      section.scrollIntoView({ behavior: "smooth" });
      dispatch(setFormUserType("athlete"));
      await dispatch(fetchRoleId("athlete"));
    }
  };

  if (loading || !builtForFamiliesSection) return null;

  return (
    <section className="bg-[#0D1D3A] py-20 px-4 md:px-12 lg:px-24">
      {/* Header */}
      <div className="text-center mb-16">
        <h2 className="text-3xl text-center font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-6">
          {parse(builtForFamiliesSection.title || "", titleParseOptions)}
        </h2>
        <p className="max-w-3xl mx-auto text-gray-300 text-lg font-medium">
          {parse(
            builtForFamiliesSection.shortDescription || "",
            shortDescriptionParseOptions
          )}
        </p>
      </div>

      {/* Content Grid */}
      <div className="max-w-7xl mx-auto flex flex-col lg:flex-row items-center gap-12">
        {/* Left Side Video */}
        <div className="w-full lg:w-1/2">
          {builtForFamiliesImages?.[0]?.fileLocation && (
            <video
              src={builtForFamiliesImages[0].fileLocation}
              muted
              loop
              playsInline
              autoPlay
              className="rounded-2xl w-full object-cover"
            />
          )}
        </div>

        {/* Right Side Text */}
        <div className="w-full lg:w-1/2 text-white space-y-6">
          <p className="text-lg text-gray-300">
            {parse(
              builtForFamiliesImages?.[0]?.fileDescription || "",
              fileDescriptionParseOptions
            )}
          </p>

          {/* <div className="space-y-2 text-base font-medium text-orange-400">
            <p>
              We’re not here to replace coaches or parents—we’re here to empower
              them.
            </p>
            <p>
              Built by a mom. Backed by smart tech. Designed for every aspiring
              athlete.
            </p>
          </div> */}

          <h2 className="text-4xl font-bold text-white mb-4">
            {parse(
              builtForFamiliesImages?.[0]?.fileTitle || "",
              fileTitleParseOptions
            )}
          </h2>

          <div className="flex flex-col sm:flex-row gap-4 sm:gap-8 mt-3 sm:mt-5">
            <Button
              variant="primaryGradient"
              size="xl"
              onClick={scrollToRegistration}
            >
              Sign up
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BuiltForFamilies;
