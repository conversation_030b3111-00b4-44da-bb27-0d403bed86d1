import { Loader2, X } from 'lucide-react';
import { useRef } from 'react';
import { toast } from 'react-toastify';
import { MdCloudUpload } from "react-icons/md";

const ImagesUpload = ({
    value,
    onChange,
    maxImages = 8,
    maxSize = 1024,
    width = '100%',
    height = 'auto',
    name = 'Image',
    imageNameRequired = false,
    loading=false,
}) => {
    const fileInputRef = useRef<HTMLInputElement | null>(null);

    const handleAddClick = () => {
        fileInputRef?.current?.click();
    };

    const handleFileChange = async (event) => {
        const files = Array.from(event.target.files);

        const validFiles = files.filter((file: any) => {
            if (file.size > maxSize * 1024 * 1024) {
                toast.warn(`${file.name} exceeds the maximum size of ${maxSize} MB.`);
                return false;
            }
            return true;
        });

        const processedFiles = await Promise.all(
            validFiles.map(
                (file: any) =>
                    new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = () => {
                            const base64String = (reader.result as string).split(',')[1];
                            resolve({
                                name: file.name,
                                url: URL.createObjectURL(file),
                                base64: base64String,
                                blob: file,
                            });
                        };
                        reader.onerror = (error) => reject(error);
                        reader.readAsDataURL(file);
                    }),
            ),
        );

        onChange([...value, ...processedFiles]);
        event.target.value = '';
    };

    const handleRemoveImage = (index, item) => {
        const updatedImages = value?.filter((_, i) => i !== index);
        onChange(updatedImages);
    };

    return (
        <div className='rounded-lg'>
            <div
                className={`grid ${maxImages === 1 ? 'grid-cols-1' : 'grid-cols-1 sm:grid-cols-3 md:grid-cols-4'
                    } gap-4`}>
                {value?.length > 0 &&
                    value?.map((file, index) => (
                        <div className='flex flex-col items-center gap-3 border-slate-300 shadow-lg'>
                            <div
                                key={index}
                                className={`relative group ${maxImages === 1 ? `max-w-md aspect-auto` : `aspect-auto w-full h-full`
                                    } `}>
                                <img
                                    src={file?.url || file?.file_path}
                                    alt={`Image ${index + 1}`}
                                    className='w-full h-full object-fill rounded-lg shadow-md transition-all duration-300 group-hover:opacity-75'
                                />
                                <button
                                    onClick={() => handleRemoveImage(index, file)}
                                    className='absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300'>
                                    <X size={16} />
                                </button>
                            </div>
                            {imageNameRequired && <p className='font-bold text-lg'>{file?.name}</p>}
                        </div>
                    ))}
                {value?.length < maxImages && (
                    <div
                        onClick={!loading ? handleAddClick : undefined}
                        className={`${maxImages === 1 ? 'aspect-auto' : 'aspect-auto'} ${width} ${height}
                        w-full h-full p-3 bg-white border-2 border-dashed border-gray-300 
                        rounded-xl flex items-center justify-center cursor-pointer
                        transition-all duration-300 hover:border-slate-500 group min-h-[140px]`}
                    >
                        {loading ? (
                            <div className="flex flex-col items-center justify-center gap-2">
                                <Loader2 className="h-8 w-8 animate-spin text-slate-400" />
                                <span className="text-sm text-gray-400">Uploading...</span>
                            </div>
                        ) : (
                            <div className="flex flex-col items-center justify-center gap-2 text-center">
                                <MdCloudUpload size={35} className="text-blue-800" />
                                <span className="text-sm font-medium text-gray-500 group-hover:text-slate-600">
                                    Click to upload image
                                </span>
                            </div>
                        )}
                    </div>
                )}
            </div>
            <input
                ref={fileInputRef}
                type='file'
                accept='image/*'
                multiple
                onChange={handleFileChange}
                className='hidden'
                name={name}
            />
        </div>
    );
};

export default ImagesUpload;
