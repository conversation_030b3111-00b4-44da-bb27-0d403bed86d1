"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON><PERSON>rigger,
} from "@/components/ui/tabs";
import { AppDispatch, RootState } from "@/store";
import { fetchGeneralSportsAnnouncements } from "@/store/slices/athlete/athleteHomeSlice";
import parse from "html-react-parser";
import { Loader2 } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Carousel, CarouselContent, CarouselItem } from "../ui/carousel";

export default function RightPanel() {
  const [currentAd, setCurrentAd] = useState(0);
  const { generalAnnouncements, sportsAnnouncements, apiStatus } = useSelector((state: RootState) => state.athleteHome)
  const dispatch = useDispatch<AppDispatch>()
  const carouselRef = useRef<any>(null)

  useEffect(() => {
    const interval = setInterval(() => {
      if (carouselRef.current) {
        carouselRef.current.scrollTo(
          (carouselRef.current.selectedScrollSnap() + 1) %
          generalAnnouncements?.length || sportsAnnouncements?.length
        )
      }
    }, 2000)

    return () => clearInterval(interval)
  }, [generalAnnouncements, sportsAnnouncements, carouselRef])

  const initialFetches = async () => {
    await dispatch(fetchGeneralSportsAnnouncements())
  }

  useEffect(() => {
    initialFetches()
  }, [dispatch])

  const ads = [
    { id: 1, image: "builtforfamileies.png", alt: "Try Connect Athlete" },
    { id: 2, image: "ca-banner.png", alt: "Train Like a Pro" },
    { id: 3, image: "builtforfamileies.png", alt: "Join Elite Community" },
  ];

  // Optional: auto-rotate ads every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentAd((prev) => (prev + 1) % ads.length);
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  const renderAnnouncements = (list, listName) => {
    return (
      <>
        {apiStatus === 'announcementsLoading' ?
          <div className="flex justify-center items-center h-full">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
          : list?.length > 0 ?
            <>
              <Carousel
                opts={{
                  align: "start",
                  loop: true,
                }}
                setApi={(api) => (carouselRef.current = api)}
                orientation="vertical"
                className="w-full max-w-xs mx-auto h-full"
              >
                <CarouselContent className="h-80 max-h-fit w-full gap-1">
                  {list?.map((item) => (
                    <CarouselItem
                      key={item?.announcement?.id}
                      className="basis-1/3"
                    >
                      <Card className="w-full shadow border hover:border-slate-300 hover:shadow-lg">
                        <CardHeader>
                          <CardTitle>
                            {parse(item?.announcement?.announcementTitle)}
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="grid gap-6">
                          {parse(item?.announcement?.announcementDescription)}
                        </CardContent>
                      </Card>
                    </CarouselItem>
                  ))}
                </CarouselContent>
              </Carousel>
            </>
            :
            <div className="flex justify-center items-center h-full">
              {listName === 'general' ? <p className="text-center p-2">
                There are currently no announcements. <br /> Please check back soon
                for updates!
              </p> :
                <p className="text-center p-2">
                  We're gearing up for something exciting! <br /> No announcements yet
                  — check back later!
                </p>}
            </div>
        }
      </>

    );
  };

  return (
    <div className="space-y-6 w-full">
      <div className="bg-white rounded-lg shadow p-4 space-y-4 w-full">
        <h2 className="text-center text-lg font-semibold">Announcements</h2>
        <Tabs defaultValue="general" className="flex flex-col justify-center items-center">
          <TabsList className="bg-blue-200 text-primary mb-5">
            {['General', 'Sports']?.map(each =>
              <TabsTrigger key={each} value={each?.toLowerCase()} className="w-full  data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:font-semibold">{each}</TabsTrigger>
            )}
          </TabsList>
          <TabsContent value="general" className="h-80">
            {renderAnnouncements(generalAnnouncements, 'general')}
          </TabsContent>

          <TabsContent value="sports" className="h-80">
            {renderAnnouncements(sportsAnnouncements, 'sports')}
          </TabsContent>
        </Tabs>
      </div>


      {/* <div className="bg-blue-50 rounded-lg p-4 shadow-lg">
        <h3 className="text-sm font-semibold text-gray-800 mb-2">Sponsored</h3>
        <div className="relative w-full rounded-lg overflow-hidden border border-gray-200 shadow">
          <img
            src={ads[currentAd].image}
            alt={ads[currentAd].alt}
            className="w-full h-full object-fill transition duration-300 ease-in-out"
          />
        </div>
      </div> */}
    </div>
  );
}