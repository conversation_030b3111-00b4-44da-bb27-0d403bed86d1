import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import { Button } from "../ui/button";
import { Calendar } from "../ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";

type CalendarProps = {
    placeholder?: string;
    mode?: "default" | "multiple" | "range" | "single";
    name?: string;
    dateValue: Date | undefined | DateRange;
    setDateFn: React.Dispatch<React.SetStateAction<any>>;
    disabled?:boolean
};

const CommonCalender = ({
    placeholder,
    mode = "single",
    dateValue,
    setDateFn,
    name,
    disabled
}: CalendarProps) => {
    const [open, setOpen] = useState(false);

    const getDisplayValue = () => {
        if (mode === "single" && dateValue instanceof Date) {
            return format(dateValue, "MMMM dd yyyy");
        }
        if (mode === "range" && typeof dateValue === "object" && dateValue !== null) {
            const { from, to } = dateValue as DateRange;
            if (from && to) return `${format(from, "MMM dd yyyy")} - ${format(to, "MMM dd yyyy")}`;
            if (from) return `${format(from, "MMM dd yyyy")}`;
        }
        return null;
    };

    const handleSelect = (selected: any) => {
        if (mode === "single") {
            if (selected instanceof Date) {
                setDateFn(selected);
                setOpen(false);
            }
        } else if (mode === "range") {
            if (selected?.from) {
                setDateFn(selected);
                if (selected?.to) {
                    setOpen(false);
                }
            }
        }
    };

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    onClick={() => setOpen((prev) => !prev)}
                    className={cn(
                        "flex items-center justify-start font-normal w-full bg-white hover:bg-white",
                        !getDisplayValue() && "text-muted-foreground"
                    )}
                    disabled={disabled}
                >
                    <CalendarIcon className="h-4 w-4" />
                    {getDisplayValue() ?? (
                        <span className="flex items-center gap-2">
                            {placeholder || "Pick Date"}
                        </span>
                    )}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
                {mode === "single" ? (
                    <Calendar
                        mode="single"
                        selected={dateValue as Date}
                        onSelect={handleSelect}
                        initialFocus
                    />
                ) : (
                    <Calendar
                        mode="range"
                        selected={dateValue as DateRange}
                        onSelect={handleSelect}
                        initialFocus
                        defaultMonth={(dateValue as DateRange)?.from}
                        numberOfMonths={2}
                    />
                )}
            </PopoverContent>
        </Popover>
    );
};

export default CommonCalender;
