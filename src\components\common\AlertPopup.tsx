import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { AlertCircle } from "lucide-react";
import { ReactNode } from "react";

interface IProps {
    trigger: ReactNode;
    alertTitle: string;
    alertContent: string;
    action: () => void
}

const AlertPopup = ({ trigger, alertTitle, alertContent, action }: IProps) => {
    return (
        <AlertDialog>
            <AlertDialogTrigger asChild>
                {trigger}
            </AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle className="flex items-center gap-1 text-lg font-semibold">
                        <AlertCircle className="text-red-600 h-5 w-5 mr-2" />                        {alertTitle}
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-primary text-md font-semibold">
                        {alertContent}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel>No</AlertDialogCancel>
                    <AlertDialogAction onClick={action}>Yes</AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    )
}
export default AlertPopup