"use client";
import { getDecodedToken } from "@/hooks/useTokenValues";
import { AppDispatch, RootState } from "@/store";
import { logout } from "@/store/slices/auth/loginSlice";
import { usePathname, useRouter } from "next/navigation";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
    const router = useRouter();
    const pathname = usePathname();
    const dispatch = useDispatch<AppDispatch>()
    const { token } = useSelector((state: RootState) => state.login);
    const publickRoutes = ['/']

    const signOutAndRedirectToHome = () => {
        dispatch(logout());
        localStorage.clear();
        router.replace("/");
    }

    useEffect(() => {
        const storedToken = token || localStorage.getItem("token");
        const decoded = getDecodedToken(storedToken);
        const now = Date.now() / 1000;

        if (!decoded || decoded.exp! < now) {
            signOutAndRedirectToHome()
        }

        const isPublicRoute = publickRoutes?.includes(pathname);

        if (!storedToken && !isPublicRoute) {
            signOutAndRedirectToHome()
        }
    }, [token, pathname]);

    return <>{children}</>;
};
