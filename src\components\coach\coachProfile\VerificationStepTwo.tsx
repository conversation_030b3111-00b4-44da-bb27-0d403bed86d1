import VerificationForm from "@/components/common/VerificationForm"
import { AppDispatch, RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { useDispatch, useSelector } from "react-redux"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CheckCircle, Circle } from "lucide-react"

const VerificationStepTwo = () => {
    const { additionalDocList } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch<AppDispatch>()

    const handleFormSubmit = (index: number) => (formData: any) => {
        // Update the specific document in the additionalDocList
        const updatedList = [...additionalDocList]
        updatedList[index] = formData
        dispatch(handleCoachInputChange({ name: 'additionalDocList', value: updatedList }))
    }

    const isDocumentComplete = (doc: any) => {
        return doc.title &&
               doc.description &&
               doc.documentLink &&
               doc.documentType &&
               doc.file &&
               doc.expirationDate
    }

    const completedDocsCount = additionalDocList.filter(isDocumentComplete).length

    return (
        <>
            <div className="flex flex-col gap-7">
                <div className="mb-4">
                    <p className="text-sm text-gray-600">
                        At least one additional document is required.
                        <span className="font-semibold"> Completed: {completedDocsCount}/3</span>
                    </p>
                </div>

                {additionalDocList?.map((item, index) => {
                    const isComplete = isDocumentComplete(item)
                    return (
                        <div key={index} className="space-y-4">
                            <div className="flex items-center gap-2">
                                {isComplete ? (
                                    <CheckCircle className="w-5 h-5 text-green-500" />
                                ) : (
                                    <Circle className="w-5 h-5 text-gray-400" />
                                )}
                                <h4 className="font-semibold text-lg">
                                    Additional Document {index + 1}
                                    {isComplete && <span className="text-green-500 text-sm ml-2">(Complete)</span>}
                                </h4>
                            </div>

                            <VerificationForm
                                data={item}
                                onSubmit={handleFormSubmit(index)}
                                step={2}
                                formId={`additional-doc-form-${index}`}
                            />

                            <div className="flex justify-end">
                                <Button
                                    type="submit"
                                    form={`additional-doc-form-${index}`}
                                    variant={isComplete ? "outline" : "default"}
                                >
                                    {isComplete ? "Update Document" : "Save Document"} {index + 1}
                                </Button>
                            </div>
                        </div>
                    )
                })}

                {completedDocsCount === 0 && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <p className="text-yellow-800 text-sm">
                            ⚠️ Please complete at least one additional document to proceed.
                        </p>
                    </div>
                )}
            </div>
        </>
    )
}
export default VerificationStepTwo