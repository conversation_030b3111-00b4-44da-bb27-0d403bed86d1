// /src/app/api/get-privacy/route.ts
import { NextResponse } from 'next/server';

export async function GET() {
    try {
        const res = await fetch("https://api.engageathlete.com/api/content/v1/getpage/4");
        const data = await res.json();

        return NextResponse.json({
            status: 200,
            data: {
                title: data.data.title,
                content: data.data.content,
            },
        });
    } catch (error) {
        return NextResponse.json({ message: "Failed to fetch privacy policy" }, { status: 500 });
    }
}
