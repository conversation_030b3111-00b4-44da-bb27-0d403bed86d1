import { RootState } from "@/store"
import { EachSearchItem, VerificationItem } from "@/utils/interfaces"
import { useSelector } from "react-redux"
import { Input } from "../ui/input"
import { Label } from "../ui/label"
import CommonCalender from "./CommonCalender"
import SearchInput from "./SearchInput"
import UploadFiles from "./UploadFiles"
import { useForm, Controller } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"
import * as yup from "yup"
import { useEffect } from "react"

// Validation Schema
const verificationSchema = yup.object().shape({
    title: yup
        .string()
        .required("Title is required")
        .min(3, "Title must be at least 3 characters")
        .max(100, "Title must not exceed 100 characters"),

    description: yup
        .string()
        .required("Description is required")
        .min(10, "Description must be at least 10 characters")
        .max(500, "Description must not exceed 500 characters"),

    documentLink: yup
        .string()
        .required("Document link is required")
        .url("Please enter a valid URL"),

    documentType: yup
        .object()
        .shape({
            value: yup.number().required(),
            label: yup.string().required()
        })
        .required("Document type is required"),

    otherType: yup
        .string()
        .when('documentType', {
            is: (documentType: any) => documentType?.label?.toLowerCase() === 'other',
            then: (schema) => schema.required("Please specify the document type"),
            otherwise: (schema) => schema
        }),

    expirationDate: yup
        .date()
        .required("Expiration date is required")
        .min(new Date(), "Expiration date must be in the future"),

    file: yup
        .mixed()
        .required("File upload is required")
        .test('fileType', 'Only PDF, JPG, and PNG files are allowed', (value: any) => {
            if (!value) return false;
            if (typeof value === 'string') return true; // For existing files
            const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
            return allowedTypes.includes(value.type);
        })
        .test('fileSize', 'File size must be less than 5MB', (value: any) => {
            if (!value) return false;
            if (typeof value === 'string') return true; // For existing files
            return value.size <= 5 * 1024 * 1024; // 5MB
        })
});

type VerificationFormData = yup.InferType<typeof verificationSchema>;

interface IProps {
    step?: number;
    data: VerificationItem;
    onSubmit: (data: VerificationFormData) => void;
    onCancel?: () => void;
    formId?: string;
}

const VerificationForm = ({ step, data, onSubmit, formId = "verification-form" }: IProps) => {
    const { documentTypesList } = useSelector((state: RootState) => state.commonSlice)

    const {
        control,
        handleSubmit,
        watch,
        formState: { errors },
        reset
    } = useForm<VerificationFormData>({
        resolver: yupResolver(verificationSchema),
        mode: 'onChange',
        defaultValues: {
            title: data?.title || "",
            description: data?.description || "",
            documentLink: data?.documentLink || "",
            documentType: data?.documentType || undefined,
            otherType: data?.otherType || "",
            expirationDate: data?.expirationDate || undefined,
            file: data?.file || undefined
        }
    });

    const watchedDocumentType = watch("documentType");

    // Update form when data prop changes
    useEffect(() => {
        reset({
            title: data?.title || "",
            description: data?.description || "",
            documentLink: data?.documentLink || "",
            documentType: data?.documentType || undefined,
            otherType: data?.otherType || "",
            expirationDate: data?.expirationDate || undefined,
            file: data?.file || undefined
        });
    }, [data, reset]);

    const handleFormSubmit = (formData: VerificationFormData) => {
        onSubmit(formData);
    };

    return (
        <form id={formId} onSubmit={handleSubmit(handleFormSubmit)}>
            <div className="flex flex-col justify-center p-4 gap-6 bg-slate-200 rounded-xl">
                {/* Title Field */}
                <div className="flex flex-col gap-2">
                    <Label>Title *</Label>
                    <Controller
                        name="title"
                        control={control}
                        render={({ field }) => (
                            <Input
                                {...field}
                                placeholder="Enter Title"
                                className={errors.title ? "border-red-500" : ""}
                            />
                        )}
                    />
                    {errors.title && (
                        <p className="text-red-500 text-sm">{errors.title.message}</p>
                    )}
                </div>

                {/* Description Field */}
                <div className="flex flex-col gap-2">
                    <Label>Description *</Label>
                    <Controller
                        name="description"
                        control={control}
                        render={({ field }) => (
                            <Input
                                {...field}
                                placeholder="Enter Description"
                                className={errors.description ? "border-red-500" : ""}
                            />
                        )}
                    />
                    {errors.description && (
                        <p className="text-red-500 text-sm">{errors.description.message}</p>
                    )}
                </div>

                {/* Document Link Field */}
                <div className="flex flex-col gap-2">
                    <Label>Document Link *</Label>
                    <Controller
                        name="documentLink"
                        control={control}
                        render={({ field }) => (
                            <Input
                                {...field}
                                type="url"
                                placeholder="https://example.com/document"
                                className={errors.documentLink ? "border-red-500" : ""}
                            />
                        )}
                    />
                    {errors.documentLink && (
                        <p className="text-red-500 text-sm">{errors.documentLink.message}</p>
                    )}
                </div>

                {/* Document Type Field */}
                <div className="flex flex-col gap-2">
                    <Label>Document Type *</Label>
                    <Controller
                        name="documentType"
                        control={control}
                        render={({ field }) => (
                            <SearchInput
                                list={documentTypesList}
                                name="documentType"
                                onChange={(_, value) => field.onChange(value)}
                                className={errors.documentType ? "border-red-500" : ""}
                                value={field.value}
                                placeholder="Select Document Type"
                            />
                        )}
                    />
                    {errors.documentType && (
                        <p className="text-red-500 text-sm">{errors.documentType.message}</p>
                    )}
                </div>

                {/* Other Document Type Field (conditional) */}
                {watchedDocumentType?.label?.toLowerCase() === 'other' && (
                    <div className="flex flex-col gap-2">
                        <Label>Other Document Type *</Label>
                        <Controller
                            name="otherType"
                            control={control}
                            render={({ field }) => (
                                <Input
                                    {...field}
                                    placeholder="Specify document type"
                                    className={errors.otherType ? "border-red-500" : ""}
                                />
                            )}
                        />
                        {errors.otherType && (
                            <p className="text-red-500 text-sm">{errors.otherType.message}</p>
                        )}
                    </div>
                )}

                {/* Expiration Date Field */}
                <div className="flex flex-col gap-2">
                    <Label>
                        Expiration Date *
                        {step === 2 && " (If document has expiration date)"}
                    </Label>
                    <Controller
                        name="expirationDate"
                        control={control}
                        render={({ field }) => (
                            <CommonCalender
                                placeholder="Pick Expiration Date"
                                mode="single"
                                dateValue={field.value}
                                setDateFn={(date) => field.onChange(date)}
                            />
                        )}
                    />
                    {errors.expirationDate && (
                        <p className="text-red-500 text-sm">{errors.expirationDate.message}</p>
                    )}
                </div>

                {/* File Upload Field */}
                <div className="flex flex-col gap-2">
                    <Label>Upload Document (PDF/JPG/PNG) *</Label>
                    <Controller
                        name="file"
                        control={control}
                        render={({ field }) => (
                            <UploadFiles
                                acceptType={['application/pdf', 'image/*']}
                                value={field.value as string | null}
                                handleRemove={() => field.onChange(null)}
                                onFileSelect={(file) => field.onChange(file)}
                                className="w-56"
                            />
                        )}
                    />
                    {errors.file && (
                        <p className="text-red-500 text-sm">{errors.file.message}</p>
                    )}
                </div>
            </div>
        </form>
    )
}
export default VerificationForm