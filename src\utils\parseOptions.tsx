import React from "react";
import { domToReact } from "html-react-parser";

export const titleParseOptions = {
  replace: (domNode: any) => {
    if (
      domNode.name === "p" ||
      domNode.name === "strong" ||
      domNode.name === "span"
    ) {
      return (
        <span className="text-3xl text-center font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-6">
          {domToReact(domNode.children, titleParseOptions)}
        </span>
      );
    }
  },
};

export const shortDescriptionParseOptions = {
  replace: (domNode: any) => {
    if (domNode.name === "p") {
      return (
        <p className="text-lg font-medium text-gray-300 mb-4">
          {domToReact(domNode.children, shortDescriptionParseOptions)}
        </p>
      );
    }
    if (domNode.name === "strong" || domNode.name === "span") {
      return (
        <span className="text-lg font-medium text-gray-300">
          {domToReact(domNode.children, shortDescriptionParseOptions)}
        </span>
      );
    }
  },
};

export const fileTitleParseOptions = {
  replace: (domNode: any) => {
    if (
      domNode.name === "p" ||
      domNode.name === "strong" ||
      domNode.name === "span"
    ) {
      return (
        <span className="text-2xl font-bold text-orange-400">
          {domToReact(domNode.children, fileTitleParseOptions)}
        </span>
      );
    }
  },
};

export const fileTestimonialTitleParseOptions = {
  replace: (domNode: any) => {
    if (
      domNode.name === "p" ||
      domNode.name === "strong" ||
      domNode.name === "span"
    ) {
      return (
        <span className="text-xl font-bold text-orange-400 leading-relaxed">
          {domToReact(domNode.children, fileTitleParseOptions)}
        </span>
      );
    }
  },
};

export const fileTitleCoachParseOptions = {
  replace: (domNode: any) => {
    if (
      domNode.name === "p" ||
      domNode.name === "strong" ||
      domNode.name === "span"
    ) {
      return (
        <span className="text-sm font-bold text-orange-400">
          {domToReact(domNode.children, fileTitleParseOptions)}
        </span>
      );
    }
  },
};

export const fileDescriptionParseOptions = {
  replace: (domNode: any) => {
    if (domNode.name === "p") {
      return (
        <p className="text-gray-200 text-base leading-relaxed mb-2">
          {domToReact(domNode.children, fileDescriptionParseOptions)}
        </p>
      );
    }
    if (domNode.name === "strong" || domNode.name === "span") {
      return (
        <span className="text-gray-200 text-base leading-relaxed">
          {domToReact(domNode.children, fileDescriptionParseOptions)}
        </span>
      );
    }
  },
};
