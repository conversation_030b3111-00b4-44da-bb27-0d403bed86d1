"use client";
import React, { useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import parse, { domToReact } from "html-react-parser";
import {
  titleParseOptions,
  fileTitleParseOptions,
  fileDescriptionParseOptions,
} from "@/utils/parseOptions";

export default function ConnectAthleteCommunity() {
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(
    null
  );

  const { athleteCommunitySection, athleteCommunityImages, loading } =
    useSelector((state: RootState) => state.homeCMS);

  if (loading || !athleteCommunitySection) return null;

  return (
    <section
      id="connect-athlete"
      className="bg-[#0D1D3A] text-white py-10 px-4 sm:px-8 md:px-16 lg:px-36 xl:px-56 w-full"
    >
      {/* Title */}
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-6">
          {parse(athleteCommunitySection.title || "", titleParseOptions)}
        </h2>
      </div>

      {/* Image Row */}
      <div className="overflow-x-auto">
        <div className="flex gap-6 w-full justify-center pb-4">
          {athleteCommunityImages.slice(0, 3).map((img, index) => (
            <div key={index} className="flex-shrink-0 w-72 text-center">
              <div
                className="relative overflow-hidden rounded-xl cursor-pointer group"
                onClick={() => setSelectedImageIndex(index)}
              >
                <Image
                  src={img.fileLocation}
                  alt={img.fileTitle}
                  width={300}
                  height={200}
                  className="transform group-hover:scale-105 transition duration-300 object-cover w-full h-48 rounded-xl"
                />
              </div>
              <p className="mt-3 text-sm text-gray-300">
                {parse(img.fileDescription || "", fileDescriptionParseOptions)}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Dialog Viewer */}
      <Dialog
        open={selectedImageIndex !== null}
        onOpenChange={() => setSelectedImageIndex(null)}
      >
        <DialogContent className="max-w-3xl w-full bg-[#0D1D3A] text-white border border-gray-700">
          {selectedImageIndex !== null && (
            <>
              <DialogHeader>
                <DialogTitle className="text-lg">
                  {parse(
                    athleteCommunityImages[selectedImageIndex].fileTitle || "",
                    fileTitleParseOptions
                  )}
                </DialogTitle>
                <DialogDescription className="text-gray-400">
                  Click outside to close
                </DialogDescription>
              </DialogHeader>
              <Image
                src={athleteCommunityImages[selectedImageIndex].fileLocation}
                alt={athleteCommunityImages[selectedImageIndex].fileTitle}
                width={800}
                height={600}
                className="rounded-xl w-full object-contain mt-4"
              />
              <p className="mt-4 text-sm text-center text-gray-300">
                {parse(
                  athleteCommunityImages[selectedImageIndex].fileDescription ||
                    "",
                  fileDescriptionParseOptions
                )}
              </p>
            </>
          )}
        </DialogContent>
      </Dialog>
    </section>
  );
}
