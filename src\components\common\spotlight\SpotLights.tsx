"use client"

import {
    Carousel,
    CarouselContent,
    CarouselItem
} from "@/components/ui/carousel"
import { useEffect, useRef } from "react"
import SpotLightItem from "./SpotLightItem"

const spotLightsList = [
    { id: 1, title: "Comming Soon", sport: "Hockey", date: "Soon" },
    { id: 2, title: "Premium Users", sport: "Soccer", date: "Jun 2025" },
    { id: 3, title: "Premium Users", sport: "Foot ball", date: "Apr 2025" },
    { id: 4, title: "Comming Soon", sport: "Basket ball", date: "Soon" },
    { id: 5, title: "Comming Soon", sport: "Field hockey", date: "Soon" },
]

const SpotLights = () => {
    const carouselRef = useRef<any>(null)

    useEffect(() => {
        const interval = setInterval(() => {
            if (carouselRef.current) {
                carouselRef.current.scrollTo(
                    (carouselRef.current.selectedScrollSnap() + 1) %
                    spotLightsList.length
                )
            }
        }, 2000)

        return () => clearInterval(interval)
    }, [])


    return (
        <div className="bg-slate-100 flex flex-col p-4 rounded-md">
            <h2 className="font-bold text-xl mx-auto pb-2">Spotlight</h2>
            <Carousel
                opts={{
                    align: "start",
                    loop: true,
                }}
                setApi={(api) => (carouselRef.current = api)}
                orientation="vertical"
                className="w-full max-w-xs mx-auto h-full"
            >
                <CarouselContent className="h-[450px] max-h-fit w-full gap-1">
                    {spotLightsList.map((each) => (
                        <CarouselItem
                            key={each.id}
                            className="basis-1/3"
                        >
                            <SpotLightItem item={each} />
                        </CarouselItem>
                    ))}
                </CarouselContent>
            </Carousel>
        </div>
    )
}

export default SpotLights
