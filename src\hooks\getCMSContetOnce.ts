import { useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import { fetchHomeCMSData } from "@/store/slices/homeCMSSlice";

export const getCMSContentOnce = () => {
  const hasFetched = useRef(false);
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    if (!hasFetched.current) {
      dispatch(fetchHomeCMSData());
      hasFetched.current = true;
    }
  }, [dispatch]);
};
