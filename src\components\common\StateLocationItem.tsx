import { Trash2 } from "lucide-react";
import { Button } from "../ui/button";
import AlertPopup from "./AlertPopup";
import { AddedStateLocationsItem } from "@/utils/interfaces";

interface IProps {
    item: AddedStateLocationsItem;
    handleDelete: (id: number) => void
}

const StateLocationItem = ({ item, handleDelete }: IProps) => {
    return (
        <>
            <li
                className="grid grid-cols-4 w-full items-center justify-between gap-6 flex-wrap bg-white shadow-md hover:shadow-lg hover:border-slate-300 rounded-lg py-2 px-2"
                key={item?.stateId}
            >
                <div className="col-span-3 flex flex-col gap-1">
                    <h6 className="text-wrap font-semibold text-secondary px-4">
                        <span className="font-semibold ">{item?.stateName}</span>
                    </h6>
                    {item?.counties?.map(each => (
                        <p className="text-wrap font-semibold px-4" key={each.countyId}>
                            {each?.countyName}  {" "} -  {" "}

                            <span className="font-light">{each?.cities?.length &&
                                each?.cities?.map(each => each?.cityName)?.join(" | ")}</span>
                        </p>
                    ))}
                </div>

                <div className="col-span-1 flex items-center justify-end">
                    <AlertPopup
                        trigger={<Button variant="destructive" size="icon"><Trash2 /></Button>}
                        alertTitle="Delete Confirmation"
                        alertContent="Are you sure, you want to delete these locations?"
                        action={() => handleDelete(item?.stateId)}
                    />
                </div>
            </li>
        </>
    )
}
export default StateLocationItem