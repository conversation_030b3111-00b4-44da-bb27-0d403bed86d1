"use client"
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from "@/components/ui/table"
import { useHandleAthleteSectionExpose } from "@/hooks/useAthleteExposeSections"
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"
import { useTokenValues } from "@/hooks/useTokenValues"
import { AppDispatch, RootState } from "@/store"
import { fetchAthletePhysStats, handleUpdateUserInput, putAthletePhysStats } from "@/store/slices/athlete/athleteProfileSlice"
import { preventSpaces } from "@/utils/validations"
import { format } from "date-fns"
import { Loader } from "lucide-react"
import { ChangeEvent, useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import UpgradePremiumSection from "../common/UpgradePremiumSection"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import { Label } from "../ui/label"
import { Skeleton } from "../ui/skeleton"
import { Switch } from "../ui/switch"

const PhysicalStats = () => {
    const { physicalStatsToggle, physStats, apiStatus, physStatsId, listOfFourPhysStats } = useSelector((state: RootState) => state.athleteProfile)
    const dispatch = useDispatch<AppDispatch>()
    const { handleToggleSections } = useHandleAthleteSectionExpose()
    const { roleId, userId, isPremiumUser } = useTokenValues()
    const { profileId } = useLocalStoredInfo()

    useEffect(() => {
        dispatch(fetchAthletePhysStats())
    }, [dispatch])

    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
        const { name, value } = event.target
        dispatch(handleUpdateUserInput({ name: 'physStats', value: { ...physStats, [name]: value } }))
    }

    const handleStatsListChange = (index: number, field: string, value: string) => {
        const copiedList = [...listOfFourPhysStats]
        const updated = copiedList?.map((item, i) =>
            i === index ? { ...item, [field]: value?.trimStart()?.replace(preventSpaces, '') } : item
        );
        dispatch(handleUpdateUserInput({ name: 'listOfFourPhysStats', value: updated }))
    };


    const handleSavePhyStats = async () => {
        const payload = {
            roleId,
            athleteId: profileId,
            userId,
            heightInFeet: physStats?.hFeet,
            heightInches: physStats?.hInches,
            weightInLbs: physStats?.weight,
            lastUpdated: format(new Date(), 'yyyy-MM-dd'),
            isHidden: !physicalStatsToggle,
        }

        const filteredPhysStatsList = listOfFourPhysStats?.filter(i => Boolean(i.name || i.unit || i.value));

        try {
            const physStatsAction = await dispatch(putAthletePhysStats(payload))
            if (putAthletePhysStats?.fulfilled?.match(physStatsAction)) {
                dispatch(handleUpdateUserInput({ name: 'physStats', value: null }))
                await dispatch(fetchAthletePhysStats())
            }
        } catch (error) {
            console.log(error)
        }
    }

    return (
        <>
            <div className="flex flex-col gap-4 bg-slate-100 p-4 rounded-lg">
                {!isPremiumUser && <UpgradePremiumSection />}
                <div className="flex items-center justify-center gap-4">
                    <h3 className="font-bold text-xl text-center">Physical Stats</h3>
                    <Switch
                        name='physicalStatsToggle'
                        checked={physicalStatsToggle}
                        onCheckedChange={(checked) => handleToggleSections("physicalStats", checked)}
                        disabled={!isPremiumUser}
                    />
                </div>
                {apiStatus === 'fetchPhyStatPending' ?
                    <div className="flex items-center gap-6">
                        <Skeleton className="h-[20px] w-full rounded-lg bg-gray-300 animate-pulse" />
                        <Skeleton className="h-[20px] w-full rounded-lg bg-gray-300 animate-pulse" />
                        <Skeleton className="h-[20px] w-full rounded-lg bg-gray-300 animate-pulse" />
                    </div>
                    :
                    <>
                        {physicalStatsToggle ?
                            <>
                                <div className="p-3 space-y-10">
                                    <div className="gap-10 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 justify-center items-end flex-wrap w-full">
                                        <div className="flex items-center gap-4 w-full">
                                            <img src={'/height.webp'} alt='Height' className="w-6" />
                                            <div className="flex flex-col gap-1  w-full">
                                                <Label>Height in Feet</Label>
                                                <Input
                                                    className="border-slate-300"
                                                    value={physStats?.hFeet}
                                                    name='hFeet'
                                                    onChange={handleChange}
                                                    placeholder="Height in Feet"
                                                    disabled={!isPremiumUser}
                                                />
                                            </div>
                                        </div>

                                        <div className="flex flex-col gap-1 w-full">
                                            <Label>Height in Inches</Label>
                                            <Input
                                                className="border-slate-300"
                                                value={physStats?.hInches}
                                                name='hInches'
                                                onChange={handleChange}
                                                placeholder="Height in Inches"
                                                disabled={!isPremiumUser}
                                            />
                                        </div>

                                        <div className="flex items-center gap-3 w-full">
                                            <img src={'/weight.webp'} alt='Weight' className="w-6" />
                                            <div className="flex flex-col gap-1 w-full">
                                                <Label>Weight in lbs</Label>
                                                <Input
                                                    className="border-slate-300 w-full"
                                                    value={physStats?.weight}
                                                    name='weight'
                                                    onChange={handleChange}
                                                    placeholder="Weight in lbs"
                                                    disabled={!isPremiumUser}
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    <div className="flex justify-center w-full">
                                        <div className="flex flex-col justify-center gap-3 w-3/4">
                                            <Label className="text-center">You may add up to 4 additional physical stats here.</Label>
                                            <Table className="min-w-[400px] bg-slate-200  p-4 rounded-lg">
                                                <TableHeader>
                                                    <TableRow className="hover:bg-transparent">
                                                        <TableHead className="text-center ">PHYSICAL STATS NAME</TableHead>
                                                        <TableHead className="text-center ">STATS UNIT</TableHead>
                                                        <TableHead className="text-center ">STATS VALUE</TableHead>
                                                    </TableRow>
                                                </TableHeader>
                                                <TableBody className="shadow-md px-4">
                                                    {listOfFourPhysStats?.map((stat, index) => (
                                                        <TableRow key={index}>
                                                            <TableCell>
                                                                <Input
                                                                    placeholder="Enter Stats Name"
                                                                    value={stat.name}
                                                                    onChange={(e) => handleStatsListChange(index, "name", e.target.value)}
                                                                    disabled={!isPremiumUser}
                                                                />
                                                            </TableCell>
                                                            <TableCell>
                                                                <Input
                                                                    placeholder="Enter Stats Unit"
                                                                    value={stat.unit}
                                                                    onChange={(e) => handleStatsListChange(index, "unit", e.target.value)}
                                                                    disabled={!isPremiumUser}
                                                                />
                                                            </TableCell>
                                                            <TableCell>
                                                                <Input
                                                                    placeholder="Enter Stats Value"
                                                                    value={stat.value}
                                                                    onChange={(e) => handleStatsListChange(index, "value", e.target.value)}
                                                                    disabled={!isPremiumUser}
                                                                />
                                                            </TableCell>
                                                        </TableRow>
                                                    ))}
                                                </TableBody>
                                            </Table>
                                        </div>
                                    </div>

                                    <div className="flex justify-end w-full">
                                        <Button className="w-16 self-end"
                                            onClick={handleSavePhyStats}
                                            disabled={(!physStats?.hFeet || !physStats?.weight) || !isPremiumUser || apiStatus === 'physStatsPending'}
                                        >
                                            {apiStatus === 'physStatsPending' ?
                                                <Loader className="animate-spin text-white w-10 h-10" />
                                                : 'Save'}
                                        </Button>
                                    </div>
                                </div>
                            </>
                            :
                            null
                        }
                    </>}
            </div>
        </>
    )
}
export default PhysicalStats