"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "../ui/button";
import {
  fetchRoleId,
  setFormUserType,
} from "@/store/slices/auth/registerSlice";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store";

// Lucide icons
import { Du<PERSON>bell, UserCog, Briefcase } from "lucide-react";

const Banners = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [showSticky, setShowSticky] = useState(false);

  const scrollToRegistration = async (
    role: "athlete" | "coach" | "business"
  ) => {
    const section = document.getElementById("registration");
    if (section) {
      section.scrollIntoView({ behavior: "smooth" });
      dispatch(setFormUserType(role));
      await dispatch(fetchRoleId(role));
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      const triggerPoint = window.innerHeight * 0.8; // triggers after 80% scroll of hero
      setShowSticky(window.scrollY > triggerPoint);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div id="hero" className="relative w-full overflow-hidden bg-black">
      {/* Sticky Top CTA */}
      {/* {showSticky && (
        <div className="fixed top-0 left-0 w-full z-50 flex justify-center sticky-cta-top">
          <div className="flex items-center gap-6 bg-black/70 backdrop-blur-sm rounded-full py-2 px-6 mt-2 border border-white/10">
            <span className="text-white font-medium text-sm sm:text-base">
              Sign up for free today
            </span>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="bg-orange-500 hover:bg-orange-600"
                onClick={() => scrollToRegistration("athlete")}
              >
                <Dumbbell className="text-white w-5 h-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="bg-orange-500 hover:bg-orange-600"
                onClick={() => scrollToRegistration("coach")}
              >
                <UserCog className="text-white w-5 h-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="bg-orange-500 hover:bg-orange-600"
                onClick={() => scrollToRegistration("business")}
              >
                <Briefcase className="text-white w-5 h-5" />
              </Button>
            </div>
          </div>
        </div>
      )} */}

      {/* Hero Section with Video */}
      <div className="relative w-full h-[60vh] sm:h-[80vh] lg:h-screen">
        <video
          id="landingVideo"
          className="absolute inset-0 w-full h-full object-cover"
          autoPlay
          loop
          muted
          playsInline
          preload="auto"
          poster="/poster.jpg"
        >
          <source src="/landing-page-videos/hero-video.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        <div className="absolute inset-0 bg-black/30 z-10 flex flex-col items-center justify-end pb-12 sm:pb-24 px-4 text-center">
          <h2 className="text-xl sm:text-2xl font-semibold text-white drop-shadow mb-4">
            Sign up for free today
          </h2>
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-8 mt-3 sm:mt-5">
            {["Athlete", "Coach", "Business"].map((role) => (
              <Button
                key={role}
                variant="primaryGradient"
                size="xl"
                onClick={() => scrollToRegistration(role.toLowerCase() as any)}
              >
                {role === "Athlete" ? "Athlete / Student" : role}
              </Button>
            ))}
          </div>
          <h1 className="text-xl sm:text-6xl font-bold text-white drop-shadow mt-8 sm:mt-16">
            Empower. Engage. Elevate.
          </h1>
        </div>
      </div>
    </div>
  );
};

export default Banners;
