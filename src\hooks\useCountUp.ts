// hooks/useCountUp.ts
import { useEffect, useState } from "react";

export function useCountUp(to: number, duration = 1500) {
    const [count, setCount] = useState(0);

    useEffect(() => {
        let start = 0;
        let startTime: number | null = null;

        const step = (timestamp: number) => {
            if (!startTime) startTime = timestamp;
            const progress = timestamp - startTime;
            const progressRatio = Math.min(progress / duration, 1);
            const currentCount = Math.floor(progressRatio * to);
            setCount(currentCount);

            if (progress < duration) {
                requestAnimationFrame(step);
            } else {
                setCount(to); // final value
            }
        };

        requestAnimationFrame(step);
    }, [to, duration]);

    return count;
}
