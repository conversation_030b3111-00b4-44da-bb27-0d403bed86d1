import { CMSSection, GalleryItem, HomeCMSState } from "@/utils/interfaces";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import axios from "axios";

const initialState: HomeCMSState = {
  loading: false,
  error: "",

  homeBannerImages: [],
  cmsSections: [],
  cmsImages: [],

  unlockSection: null,
  unlockImages: [],

  howItWorksSection: null,
  howItWorksImages: [],

  testimonialSection: null,
  testimonialImages: [],

  mission1Section: null,
  mission1Images: [],

  mission2Section: null,
  mission2Images: [],

  athleteInActionSection: null,
  athleteInActionImages: [],

  athleteCommunitySection: null,
  athleteCommunityImages: [],

  youthEcosystemSection: null,
  youthEcosystemImages: [],

  ourCoachesSection: null,
  ourCoachesImages: [],

  smartTechnologySection: null,
  smartTechnologyImages: [],

  mobileStoreSection: null,
  mobileStoreImages: [],

  builtForFamiliesSection: null,
  builtForFamiliesImages: [],

  homeAnnouncementSection: null,
  homeAnnouncementImages: [],

  ourImpactSection: null,
  ourImpactImages: [],

  connectAthleteEcoSystemSection: null,
  connectAthleteEcoSystemImages: [],

  ourSportsSection: null,
  ourSportsImages: [],

  aboutSection: null,
  aboutVideo: []
};

const apiUrl = "https://api.engageathlete.com/api/content/v2";

// Single API call
export const fetchHomeCMSData = createAsyncThunk(
  "CMS/fetchHomeCMSData",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_CONTENT_API_URL}/getpage/1`
      );
      return fulfillWithValue(response.data.data);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const homeCMSSlice = createSlice({
  name: "homeCMS",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchHomeCMSData.pending, (state) => {
        state.loading = true;
      })
      .addCase(
        fetchHomeCMSData.fulfilled,
        (state, action: PayloadAction<any>) => {
          state.loading = false;
          const data = action.payload;

          state.cmsSections = data.cmssections || [];
          state.cmsImages = data.cmsgalleries || [];

          state.homeBannerImages = data.cmsgalleries?.filter(
            (img: GalleryItem) => img.fileType === "Banner Image"
          );

          const extract = (sectionId: number) => {
            const section = data.cmssections.find(
              (sec: CMSSection) => sec.id === sectionId
            );
            const images = data.cmsgalleries.filter(
              (img: GalleryItem) => img.cmssectionId === sectionId
            );
            return { section, images };
          };

          const unlock = extract(3);
          state.unlockSection = unlock.section;
          state.unlockImages = unlock.images;

          const howItWorks = extract(5);
          state.howItWorksSection = howItWorks.section;
          state.howItWorksImages = howItWorks.images;

          const testimonial = extract(25);
          state.testimonialSection = testimonial.section;
          state.testimonialImages = testimonial.images;

          const mission1 = extract(23);
          state.mission1Section = mission1.section;
          state.mission1Images = mission1.images;

          const mission2 = extract(24);
          state.mission2Section = mission2.section;
          state.mission2Images = mission2.images;

          const athleteAction = extract(26);
          state.athleteInActionSection = athleteAction.section;
          state.athleteInActionImages = athleteAction.images;

          const community = extract(27);
          state.athleteCommunitySection = community.section;
          state.athleteCommunityImages = community.images;

        const youthEcoSystem = extract(21);
        state.youthEcosystemSection = youthEcoSystem.section;
        state.youthEcosystemImages = youthEcoSystem.images;

        const ourSports = extract(20);
        state.ourSportsSection = ourSports.section;
        state.ourSportsImages = ourSports.images;

        const aboutUs = extract(34);
        state.aboutSection = aboutUs.section;
        state.aboutVideo = aboutUs.images;

          const ourCoaches = extract(22);
          state.ourCoachesSection = ourCoaches.section;
          state.ourCoachesImages = ourCoaches.images;

          const smartTechnology = extract(29);
          state.smartTechnologySection = smartTechnology.section;
          state.smartTechnologyImages = smartTechnology.images;

          const mobileStore = extract(32);
          state.mobileStoreSection = mobileStore.section;
          state.mobileStoreImages = mobileStore.images;

          const builtForFamilies = extract(30);
          state.builtForFamiliesSection = builtForFamilies.section;
          state.builtForFamiliesImages = builtForFamilies.images;

          const homeAnnouncement = extract(31);
          state.homeAnnouncementSection = homeAnnouncement.section;
          state.homeAnnouncementImages = homeAnnouncement.images;

          const ourImpact = extract(24);
          state.ourImpactSection = ourImpact.section;
          state.ourImpactImages = ourImpact.images;

          const connectAthleteEcoSystem = extract(33);
          state.connectAthleteEcoSystemSection =
            connectAthleteEcoSystem.section;
          state.connectAthleteEcoSystemImages = connectAthleteEcoSystem.images;
        }
      )
      .addCase(fetchHomeCMSData.rejected, (state, action) => {
        state.loading = false;
        state.error = (action.payload as string) || "Something went wrong";
      });
  },
});

export default homeCMSSlice.reducer;
