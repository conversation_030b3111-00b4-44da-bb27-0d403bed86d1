import { Card, CardContent } from "@/components/ui/card"
import { Eye, Heart, MessageSquare } from "lucide-react"


export default function CoachCardItem() {
    return (
        <Card className="flex flex-col w-full p-4 gap-4 shadow-md">
            <CardContent className="flex-1 p-0 flex gap-4 w-full">
                {/* Left: Profile Pic */}
                <div className="w-1/6  h-28 rounded-full overflow-hidden">
                    <img
                        src={'/user.svg'}
                        alt={'Profile'}
                        className="object-cover h-full"
                    />
                </div>

                {/* Info Section */}
                <div className="flex flex-col gap-1 w-full">
                    <div className="flex flex-wrap items-center justify-between gap-5">
                        <h2 className="text-lg font-semibold">{'John Deo'}</h2>
                        <div className="flex items-center gap-3">
                            <Heart className="w-6 h-6  hover:text-red-500 cursor-pointer" />
                            <MessageSquare className="w-6 h-6 hover:text-blue-500 cursor-pointer" />
                            <Eye className="w-6 h-6 hover:text-primary cursor-pointer" />
                        </div>
                    </div>

                    <p className="text-sm">
                        Cricket - {' '}
                        <span>National</span>
                        <span>Batting | Bowling | Fielding</span>
                    </p>

                    <p className="text-sm">
                        New Jersey - Abc | def | ghi
                    </p>

                    <p>
                        Gender Coach Trains - Female | Male
                    </p>

                    <p className="text-sm">
                        Age Groups: 1-5 | 5-6 | 6-8
                    </p>

                    <p>Coach Type : Coach Type1 | Coach Type2</p>
                    <p>Coach Background : Coach Background1 | Coach Background2</p>
                    <p>Training/ Offering : Coach Offering1 | Coach Offering2</p>
                    <p>Coach Intent : Coach Intent1 | Coach Intent2</p>
                </div>
            </CardContent>
        </Card>
    )
}
