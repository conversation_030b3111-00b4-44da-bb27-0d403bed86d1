import AthleteProfile from "@/components/athleteProfile/AthleteProfile"
import ClientGuard from "@/components/ClientGuard"
import { notFound } from "next/navigation";

const AthleteProfilePublicPage = ({ params }: { params: { slug: string } }) => {
    const { slug } = params;
    const validSlugPattern = /^(\d+)-([a-zA-Z]+)-([a-zA-Z]+)$/;
    if (!validSlugPattern.test(slug)) return notFound();

    return (
        <>
            Athlete Public Profile
        </>
    )
}
export default AthleteProfilePublicPage