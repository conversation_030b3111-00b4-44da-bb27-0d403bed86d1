"use client";
import React, { useRef, useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import parse from "html-react-parser";
import {
  titleParseOptions,
  shortDescriptionParseOptions,
  fileTitleParseOptions,
  fileDescriptionParseOptions,
} from "@/utils/parseOptions";
import { Maximize } from "lucide-react";

const ConnectAthleteInAction = () => {
  const { athleteInActionSection, athleteInActionImages, loading } =
    useSelector((state: RootState) => state.homeCMS);

  const [popupVideo, setPopupVideo] = useState<null | {
    fileLocation: string;
    fileTitle: string;
  }>(null);

  const popupVideoRef = useRef<HTMLVideoElement | null>(null);

  // Autoplay when popup opens
  useEffect(() => {
    if (popupVideoRef.current) {
      popupVideoRef.current.load(); // Ensure source resets
      popupVideoRef.current
        .play()
        .catch((err) => console.warn("Autoplay failed:", err));
    }
  }, [popupVideo]);

  const handleFullscreenClick = (card: {
    fileLocation: string;
    fileTitle?: string;
  }) => {
    setPopupVideo({
      fileLocation: card.fileLocation,
      fileTitle: card.fileTitle || "",
    });
  };

  const handleClosePopup = () => {
    setPopupVideo(null);
  };

  if (loading || !athleteInActionSection) return null;

  return (
    <section
      id="connect-athlete"
      className="relative bg-[#0D1D3A] text-white py-10 px-4 sm:px-8 md:px-16 lg:px-36 xl:px-56 w-full"
    >
      {/* Section Heading */}
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-6">
            {parse(athleteInActionSection.title || "", titleParseOptions)}
        </h2>
        <p className="cms-text text-lg text-gray-300 leading-relaxed max-w-3xl mx-auto">
          {parse(
            athleteInActionSection.description || "",
            shortDescriptionParseOptions
          )}
        </p>
      </div>

      {/* Scrollable Video Cards */}
      <div className="flex gap-6 overflow-x-auto snap-x snap-mandatory pb-4">
        {athleteInActionImages.slice(-3).map((card, index) => (
          <div
            key={index}
            className="group min-w-[300px] max-w-sm bg-[#122347] rounded-2xl shadow-lg snap-start overflow-hidden relative"
          >
            <video
              className="w-full h-48 object-cover"
              src={card.fileLocation}
              muted
              loop
              playsInline
              onMouseEnter={(e) => e.currentTarget.play()}
              onMouseLeave={(e) => e.currentTarget.pause()}
            />

            {/* Fullscreen Icon */}
            <button
              onClick={() => handleFullscreenClick(card)}
              className="absolute top-2 right-2 p-2 bg-black/50 rounded-full text-white opacity-0 group-hover:opacity-100 transition-opacity"
              aria-label="Open Fullscreen"
            >
              <Maximize size={20} />
            </button>

            <div className="p-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                🎙️
                {parse(card.fileTitle || "", fileTitleParseOptions)}
              </h3>
              <p className="text-sm text-gray-300 mt-2 flex items-start gap-2">
                📄
                {parse(card.fileDescription || "", fileDescriptionParseOptions)}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Video Popup */}
      {popupVideo && (
        <div className="fixed inset-0 bg-black/70 flex justify-center items-center z-50">
          <div className="w-full max-w-3xl relative px-4">
            <video
              ref={popupVideoRef}
              className="w-full rounded-lg shadow-2xl"
              src={popupVideo.fileLocation}
              loop
              controls
              playsInline
            />
            <button
              className="absolute top-2 right-2 text-white text-2xl font-bold bg-black/20 rounded-full px-3 py-1 hover:bg-black/80"
              onClick={handleClosePopup}
            >
              ✕
            </button>
          </div>
        </div>
      )}
    </section>
  );
};

export default ConnectAthleteInAction;
