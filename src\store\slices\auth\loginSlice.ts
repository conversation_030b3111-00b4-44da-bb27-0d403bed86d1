import axiosInstance from "@/utils/axiosInstance";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";
import { toast } from "react-toastify";

interface LoginData {
  email: string;
}

export interface UserResponse {
  id: number;
  email: string;
  roleId: number;
  [key: string]: any;
}

interface LoginState {
  apiStatus: string;
  loading: boolean;
  error: string | null;
  success: boolean;
  token: string | null;
  user: UserResponse | null;
  profileData: any | null;
  logInData: LoginData;
}

const initialState: LoginState = {
  loading: false,
  error: null,
  success: false,
  token: null,
  user: null,
  profileData: null,
  logInData: { email: "" },
  apiStatus: "",
};

const apiUrl = "https://api.engageathlete.com/api/user-profile/v2/";

export const sendOtp = createAsyncThunk(
  "auth/sendOtp",
  async (email: string, { rejectWithValue }) => {
    try {
      const res = await fetch(`${apiUrl}login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data.message || "Failed to send OTP");
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || "An unknown error occurred");
    }
  }
);

export const verifyOtpLogin = createAsyncThunk(
  "auth/verifyOtpLogin",
  async (
    { userId, roleId, otp }: { userId: number; roleId: number; otp: string },
    { rejectWithValue }
  ) => {
    try {
      const res = await fetch(`${apiUrl}login/verifyOTP`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ userId, roleId, otp }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data.message || "OTP verification failed");
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || "OTP verification failed");
    }
  }
);

export const updateTermsAcceptedAt = createAsyncThunk(
  "auth/updateTermsAcceptedAt",
  async (userId: number, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem("token");
      const res = await fetch(`${apiUrl}updateTermsAcceptedAt/${userId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      const data = await res.json();
      if (!res.ok)
        throw new Error(data.message || "Failed to update terms acceptance");

      return data;
    } catch (error: any) {
      return rejectWithValue(error.message || "An error occurred");
    }
  }
);

export const postVerifyProfileEmail = createAsyncThunk(
  "verifyEmail/postVerifyProfileEmail",
  async (payload: { email: string }, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/login`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        console.log(error);
        return rejectWithValue(
          error?.message || "Network error or no response"
        );
      }
    }
  }
);

export const putSubmitOTP = createAsyncThunk(
  "verifyEmail/putSubmitOTP",
  async (
    payload: { userId: number; roleId: number; otp: string },
    { fulfillWithValue, rejectWithValue }
  ) => {
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/login/verifyOTP`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.message || "Network error or no response"
        );
      }
    }
  }
);

const loginSlice = createSlice({
  name: "login",
  initialState,
  reducers: {
    handleLoginError: (state, action) => {
      state.error = action.payload;
    },
    logout: (state) => {
      localStorage.clear();
      state.user = null;
      state.token = null;
      state.success = false;
      state.profileData = null;
    },
    handleUpdateTokenUserValues: (state, action) => {
      const {token, user, profile} = action.payload
      state.token = token;
      state.user = user;
      state.profileData = profile
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(sendOtp.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(sendOtp.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) {
          state.user = action.payload.user;
          state.profileData = action.payload.profileData;
        }
      })
      .addCase(sendOtp.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(verifyOtpLogin.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyOtpLogin.fulfilled, (state, action) => {
        state.loading = false;
        state.success = true;
        if (action.payload) {
          state.token = action.payload.token;
          state.user = action.payload.user;
          state.profileData = action.payload.profileData;
        }
      })
      .addCase(verifyOtpLogin.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(updateTermsAcceptedAt.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateTermsAcceptedAt.fulfilled, (state) => {
        state.loading = false;
        toast.success("Terms accepted successfully!");
      })
      .addCase(updateTermsAcceptedAt.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error(action.payload as string);
      })
      .addCase(postVerifyProfileEmail.pending, (state) => {
        state.apiStatus = "verifyPending";
      })
      .addCase(postVerifyProfileEmail.fulfilled, (state, action) => {
        state.apiStatus = "verifySuccess";
        if (action.payload) {
          state.user = action.payload.user;
          state.profileData = action.payload.profileData;
        }
        toast.success("OTP Sent to your email please check");
      })
      .addCase(postVerifyProfileEmail.rejected, (state, action) => {
        state.apiStatus = "verifyReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) ||
            "Failed to verify email please try again"
        );
      })
      .addCase(putSubmitOTP.pending, (state) => {
        state.apiStatus = "otpSbmtPending";
      })
      .addCase(putSubmitOTP.fulfilled, (state, action) => {
        state.apiStatus = "otpSbmtSuccess";
        if (action.payload) {
          state.token = action.payload?.token;
          state.user = action.payload?.user;
          state.profileData = action.payload?.profileData;
          localStorage.setItem(
            "userInfo",
            JSON.stringify(action.payload?.user)
          );
          localStorage.setItem(
            "profileInfo",
            JSON.stringify(action.payload?.profileData)
          );
          localStorage.setItem("profileId", action.payload?.profileData?.id);
          localStorage.setItem("token", action.payload?.token);
          localStorage.setItem("userId", action.payload?.user?.id);
          localStorage.setItem("roleId", action.payload?.user?.roleId);
        }
        toast.success("OTP verified successfully!");
      })
      .addCase(putSubmitOTP.rejected, (state, action) => {
        state.apiStatus = "otpSbmtReject";
        state.error = action.payload as string;
        toast.error((action.payload as string) || "Failed to verify OTP");
      });
  },
});

export const { handleLoginError, handleUpdateTokenUserValues, logout } = loginSlice.actions;
export default loginSlice.reducer;