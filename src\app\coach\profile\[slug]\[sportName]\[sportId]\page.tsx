import ClientGuard from "@/components/ClientGuard"
import CoachSportProfile from "@/components/coach/coachSportProfile/CoachSportProfile"
import { notFound } from "next/navigation"

interface CoachProfilePageProps {
    params: { slug: string, sportName: string }
}

const CoachSportProfilePage = ({ params }: CoachProfilePageProps) => {
    const { slug, sportName } = params
    const validSlugPattern = /^(\d+)-([a-zA-Z]+)-([a-zA-Z]+)$/;
    if (!validSlugPattern.test(slug as string)) return notFound();
    if (!/^[a-zA-Z]+$/.test(sportName as string)) return notFound();
    // if (!/^\d+$/.test(sportId)) return notFound();

    return (
        <ClientGuard allowedRoles={[3]}>
            <CoachSportProfile params={params} />
        </ClientGuard>
    )
}

export default CoachSportProfilePage
