'use client'
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>er,
    <PERSON><PERSON><PERSON><PERSON>er,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Di<PERSON>Trigger
} from "@/components/ui/dialog"
import {
    Toolt<PERSON>,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip"
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"
import { useTokenValues } from "@/hooks/useTokenValues"
import { AppDispatch } from "@/store"
import { fetchAthleteProfileUrl } from "@/store/slices/athlete/athleteProfileSlice"
import { putProfileUrl } from "@/store/slices/commonSlice"
import { generateProfileUrl } from "@/utils/commonFunctions"
import { preventSpaces } from "@/utils/validations"
import { PencilLine } from "lucide-react"
import { useEffect, useState } from "react"
import { useDispatch } from "react-redux"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import { Switch } from "../ui/switch"

interface IProps {
    toggleProfile?: boolean;
    handleToggleProfile?: (checked: boolean) => void;
    fetchedProfileUrl?: string;
}

const ProfileUrl = ({ toggleProfile, fetchedProfileUrl, handleToggleProfile }: IProps) => {
    const { profileData } = useLocalStoredInfo()
    const { userId, roleType } = useTokenValues()
    const profileUrlQuery = profileData ? generateProfileUrl(profileData) : '';
    const profileCompleteUrl = `${process.env.NEXT_PUBLIC_CONNECT_ATHLETE_URL}${profileUrlQuery}`
    const [profileUrl, setProfileUrl] = useState(profileCompleteUrl);
    const [preferredName, setPreferredName] = useState('')
    const [openModal, setOpenModal] = useState(false);
    const dispatch = useDispatch<AppDispatch>()

    useEffect(() => {
        profileCompleteUrl && setProfileUrl(profileCompleteUrl)
    }, [profileData, profileCompleteUrl])

    useEffect(() => {
        fetchedProfileUrl && setProfileUrl(fetchedProfileUrl)
    }, [fetchedProfileUrl])


    useEffect(() => {
        if (!openModal) {
            setPreferredName('')
        }
    }, [openModal])


    const handleSave = async () => {
        if (profileUrl) {
            const payload = {
                profileUrl: `${process.env.NEXT_PUBLIC_CONNECT_ATHLETE_URL}/${roleType}/${userId}-${preferredName?.trim()}`
            }
            try {
                const resultAction = await dispatch(putProfileUrl(payload))
                if (putProfileUrl.fulfilled.match(resultAction)) {
                    setOpenModal(false)
                    await dispatch(fetchAthleteProfileUrl())
                }
            } catch (error) {
                console.log(error)
            }
        }
    };

    return (
        <>
            <div className="flex flex-col gap-2 justify-center">
                <div className="flex items-center justify-center gap-2">
                    <a
                        href={profileUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-full md:w-auto break-words text-center text-wrap font-semibold text-xl hover:text-blue-600 hover:underline"
                    >
                        {profileUrl}
                    </a>
                    <Dialog open={openModal} onOpenChange={() => setOpenModal(!openModal)}>
                        <TooltipProvider>
                            <Tooltip>
                                <DialogTrigger asChild>
                                    <TooltipTrigger asChild>
                                        <Button size="icon" variant="outline"><PencilLine /> </Button>
                                    </TooltipTrigger>
                                </DialogTrigger>
                                <TooltipContent>
                                    <p>You can edit athlete preferred name</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>

                        <DialogContent className="sm:max-w-md" onInteractOutside={(event) => event.preventDefault()}>
                            <DialogHeader>
                                <DialogTitle>Edit Profile Url</DialogTitle>
                            </DialogHeader>

                            <div className="grid gap-4 py-4 w-full">
                                <div className="grid w-full gap-2">
                                    <label htmlFor="profile-url" className="text-sm font-medium">
                                        Your Profile Url Preferred Name
                                    </label>
                                    <Input
                                        className="w-full"
                                        id="profile-url"
                                        placeholder="Enter preferred name"
                                        value={preferredName}
                                        onChange={(e) => {
                                            const sanitizedValue = e.target.value
                                                ?.trimStart()
                                                ?.replace(preventSpaces, '');
                                            setPreferredName(sanitizedValue)
                                        }}
                                    />
                                    {preferredName &&
                                        <span className="text-sm text-wrap break-words break-all w-full text-green-600">
                                            Your URL : {process.env.NEXT_PUBLIC_CONNECT_ATHLETE_URL}/{roleType}/{userId}-{preferredName}
                                        </span>}
                                </div>
                            </div>

                            <DialogFooter className="flex justify-end gap-2">
                                <DialogClose asChild>
                                    <Button variant="outline">Close</Button>
                                </DialogClose>
                                <Button onClick={handleSave} disabled={!preferredName}>Save</Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                </div>
                <div className="flex gap-6 items-center  justify-center">
                    <TooltipProvider>
                        <Tooltip>
                            {/* <DialogTrigger asChild> */}
                            <TooltipTrigger asChild>
                                <img src={true ? "/unlock-password.svg" : '/lock-password.svg'} alt={true ? 'Unlocked' : 'Locked'} className="h-8 fill-current cursor-pointer" />
                            </TooltipTrigger>
                            {/* </DialogTrigger> */}
                            <TooltipContent>
                                <p>Protect Profile With Password</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger>
                                <Switch className="" checked={toggleProfile} onCheckedChange={handleToggleProfile} />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>{"Hide/Unhide Athlete Profile"}</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
            </div>
        </>
    )
}
export default ProfileUrl