import axiosInstance from "@/utils/axiosInstance";
import { AthleteProfileTypes, FetchedSocialMedia } from "@/utils/interfaces";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";
import { toast } from "react-toastify";

const initialState: AthleteProfileTypes = {
  apiStatus: "",
  error: "",
  profileUrl: "",
  profileToggle: false,
  profileCardData: null,
  isBioEditable: false,
  bio: "",
  socialMediaToggle: true,
  quickLinksToggle: true,
  physicalStatsToggle: true,
  physStats: null,
  listOfFourPhysStats: [
    { name: "", unit: "", value: "" },
    { name: "", unit: "", value: "" },
    { name: "", unit: "", value: "" },
    { name: "", unit: "", value: "" },
  ],
  openVirtualSession: true,
  athleteSocialMediaList: [
    {
      id: "X",
      icon: "/X.jpeg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "Instagram",
      icon: "/instagram.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "Facebook",
      icon: "/facebook.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "Youtube",
      icon: "/youtube.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
  ],
  athleteQuickLinksList: [],
  toggleGallery: true,
  athleteGalleryList: [],
  mySportsList: [],
  sportProfileId: null,
  selectedGrowthList: [],
  achieveOrAccomplish: "",
  toggleAchievements: true,
  achievementData: null,
  addedAchievementsList: [],
  selectedState: null,
  selectedCounties: [],
  selectedLocations: [],
  athleteAddedStateLocationsList: [],
  athleteLearning: null,
  selectedLearingTopicsList: [],
  toggleContactSection: true,
  parentGuardianData: null,
  toggleSchoolView: true,
  physStatsId: null,
  selectedAchievementId: null,
  isVerifyEmail: false,
};

export const putAthleteIntro = createAsyncThunk(
  "athleteIntro/putAthleteIntro",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/intro-section/${userId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(
          error.response.data?.message ||
            error?.response?.data?.errors?.join(",") ||
            "Server error"
        );
      }
    }
  }
);

export const fetchAthleteIntro = createAsyncThunk(
  "athleteIntro/fetchAthleteIntro",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/intro-section/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const putAthleteBio = createAsyncThunk(
  "athleteBio/putAthleteBio",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/bio-section/${userId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(
          error.response.data?.message ||
            error?.response?.data?.errors?.join(",") ||
            "Server error"
        );
      }
    }
  }
);

export const fetchAthleteBio = createAsyncThunk(
  "athleteBio/fetchAthleteBio",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/bio-section/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const postAthleteSocialMedia = createAsyncThunk(
  "athleteSocialMedia/postAthleteSocialMedia",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athletesocialmedia`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(
          error.response.data?.message ||
            error?.response?.data?.errors?.join(",") ||
            "Server error"
        );
      }
    }
  }
);

export const fetchAthleteSocialMediaLinks = createAsyncThunk(
  "athleteSocialMedia/fetchAthleteSocialMediaLinks",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athletesocialmedia/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const postAthleteQuickLinks = createAsyncThunk(
  "athleteQuickLinks/postAthleteQuickLinks",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/addvideo`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(
          error.response.data?.message ||
            error?.response?.data?.errors?.join(",") ||
            "Server error"
        );
      }
    }
  }
);

export const fetchAthleteQuickLinks = createAsyncThunk(
  "athelteQuickLinks/fetchAthleteQuickLinks",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/getUserVideos/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const putAthleteQuickLink = createAsyncThunk(
  "athleteQuickLinks/putAthleteQuickLink",
  async ({ payload, linkId }: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/updatevideo/${linkId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(
          error.response.data?.message ||
            error?.response?.data?.errors?.join(",") ||
            "Server error"
        );
      }
    }
  }
);

export const deleteAthleteQuickLink = createAsyncThunk(
  "athelteQuickLinks/deleteAthleteQuickLink",
  async (linkId: number, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.delete(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/deletevideo/${linkId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const postAthleteGallery = createAsyncThunk(
  "athleteGallery/postAthleteGallery",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_CONTENT_API_URL}/upload-multiplefiles`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const fetchAthleteGallery = createAsyncThunk(
  "athleteGallery/fetchAthleteGallery",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athletegallery`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const deleteAthleteGalleryItem = createAsyncThunk(
  "athelteGallery/deleteAthleteGalleryItem",
  async (galleryId: number, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_CONTENT_API_URL}/delete-file/${galleryId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const putAthletePhysStats = createAsyncThunk(
  "athletePhysStats/putAthletePhysStats",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athlete-physical-stats/${userId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const fetchAthletePhysStats = createAsyncThunk(
  "athletePhysStats/fetchAthletePhysStats",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athlete-physical-stats/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const postAthleteLearningHighLight = createAsyncThunk(
  "athleteLearning/postAthleteLearningHighLight",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteLearningHighlight/${userId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const fetchAthleteLearningHighLights = createAsyncThunk(
  "learning/fetchAthleteLearningHighLights",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteLearningHighlight/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const postAthleteAchievement = createAsyncThunk(
  "athleteLearning/postAthleteAchievement",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteAchievementsOffField`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        if (response?.status === 413) {
          return rejectWithValue("Upload failed: File too large.");
        }
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue("Unexpected error occurred");
    }
  }
);

export const fetchAthleteAchievement = createAsyncThunk(
  "achievement/fetchAthleteAchievement",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteAchievementsOffField/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const putAthleteAchievements = createAsyncThunk(
  "athleteAchievement/putAthleteAchievements",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteAchievementsOffField/${userId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue("Unexpected error occurred");
    }
  }
);

export const deleteAthleteAchievements = createAsyncThunk(
  "athleteAchievement/deleteAthleteAchievements",
  async (achievementId: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.delete(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteAchievementsOffField/${achievementId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const fetchAthleteAllSports = createAsyncThunk(
  "athleteSports/fetchAthleteAllSports",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportsInfo/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const putAthleteGrowthInterests = createAsyncThunk(
  "growthInterests/putAthleteGrowthInterests",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteLearningDev/${userId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const fetchAthleteGrowthInterests = createAsyncThunk(
  "growthInterests/fetchAthleteGrowthInterests",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteLearningDev/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const putAthleteOpenSession = createAsyncThunk(
  "athleteOpenSession/putAthleteOpenSession",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athlete-virtual-session/${userId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const fetchAthleteOpenSession = createAsyncThunk(
  "athleteOpenSession/fetchAthleteOpenSession",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athlete-virtual-session/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const putAthleteToggleSections = createAsyncThunk(
  "exposeSections/putAthleteToggleSections",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/profile-expose-section`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      return rejectWithValue("Unexpected error occurred");
    }
  }
);

export const fetchAthleteToggleSections = createAsyncThunk(
  "exposeSections/fetchAthleteToggleSections",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/profile-expose-section/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const putAthleteParentGuardian = createAsyncThunk(
  "parentGuardian/putAthleteParentGuardian",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/parent-guardian-contact/${userId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const fetchAthleteParentGuardian = createAsyncThunk(
  "parentGuardian/fetchAthleteParentGuardian",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/parent-guardian-contact/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const fetchAthleteProfileUrl = createAsyncThunk(
  "profileUrl/fetchAthleteProfileUrl",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/profile-url/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const putAthleteLocations = createAsyncThunk(
  "athleteLocations/putAthleteLocations",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athlete-training-locations/${userId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
    }
  }
);

export const fetchAthleteLocations = createAsyncThunk(
  "athleteLocations/fetchAthleteLocations",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athlete-training-locations/${userId}/grouped`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const deleteAthleteLocations = createAsyncThunk(
  "athleteLocations/deleteAthleteLocations",
  async (stateId: number, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.delete(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athlete-training-locations/${userId}/state/${stateId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

const athleteProfileSlice = createSlice({
  name: "athleteProfile",
  initialState,
  reducers: {
    handleUpdateUserInput: (state, action) => {
      const { name, value } = action.payload;
      state[name] = value;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAthleteProfileUrl.pending, (state) => {
        state.apiStatus = "";
      })
      .addCase(fetchAthleteProfileUrl.fulfilled, (state, action) => {
        state.apiStatus = "";
        state.profileUrl = action.payload?.profileUrl;
      })
      .addCase(fetchAthleteProfileUrl.rejected, (state, action) => {
        state.apiStatus = "";
        state.error = action.payload as string;
      })
      .addCase(putAthleteIntro.pending, (state) => {
        state.apiStatus = "putIntroPending";
      })
      .addCase(putAthleteIntro.fulfilled, (state, action) => {
        state.apiStatus = "putIntroSuccess";
        toast.success("Athlete details updated successfully!");
      })
      .addCase(putAthleteIntro.rejected, (state, action) => {
        state.apiStatus = "putIntroFailed";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to update Athlete details"
        );
      })
      .addCase(fetchAthleteIntro.pending, (state) => {
        state.apiStatus = "fetchAthleteIntroLoading";
      })
      .addCase(fetchAthleteIntro.fulfilled, (state, action) => {
        state.apiStatus = "";
        if (action.payload) {
          const {
            preferredAthleteName,
            lastName,
            profileImg,
            ageGroup,
            ageGroupId,
            gender,
            blurb,
            topPrimarySports,
          } = action.payload;
          const formattedData = {
            firstName: preferredAthleteName,
            lastName: lastName,
            blurb: blurb,
            ageGroup: {
              value: ageGroupId,
              label: ageGroup,
            },
            gender: {
              value: gender === "Female" ? 2 : 1,
              label: gender,
            },
            profileImage: profileImg,
            primarySportsList: topPrimarySports,
          };
          state.profileCardData = formattedData;
        }
      })
      .addCase(fetchAthleteIntro.rejected, (state, action) => {
        state.apiStatus = "";
        state.error = action.payload as string;
      })
      .addCase(putAthleteBio.pending, (state) => {
        state.apiStatus = "putBioPending";
      })
      .addCase(putAthleteBio.fulfilled, (state) => {
        state.apiStatus = "putBioSuccess";
        toast.success("Bio updated successfully!");
        state.isBioEditable = false;
      })
      .addCase(putAthleteBio.rejected, (state, action) => {
        state.apiStatus = "putBioFailed";
        state.error = action.payload as string;
        toast.error((action.payload as string) || "Failed to update bio");
      })
      .addCase(fetchAthleteBio.pending, (state) => {
        state.apiStatus = "fetchBioPending";
      })
      .addCase(fetchAthleteBio.fulfilled, (state, action) => {
        state.apiStatus = "fetchBioSuccess";
        state.bio = action.payload?.bio;
      })
      .addCase(fetchAthleteBio.rejected, (state, action) => {
        state.apiStatus = "fetchBioFailed";
        state.error = action.payload as string;
      })
      .addCase(postAthleteSocialMedia.pending, (state) => {
        state.apiStatus = "socialMediaPending";
      })
      .addCase(postAthleteSocialMedia.fulfilled, (state) => {
        state.apiStatus = "socialMediaSuccess";
        toast.success("Social media link added successfully!");
      })
      .addCase(postAthleteSocialMedia.rejected, (state, action) => {
        state.apiStatus = "socialMediaReject";
        state.error = action.payload as string;
        toast.error("Failed to add social media");
      })
      .addCase(fetchAthleteSocialMediaLinks.pending, (state) => {
        state.apiStatus = "fetchAthleteSocialMPending";
      })
      .addCase(fetchAthleteSocialMediaLinks.fulfilled, (state, action) => {
        state.apiStatus = "fetchAthleteSocialMSuccess";
        const updatedFetchedList = action.payload?.map(
          (item: FetchedSocialMedia) => ({
            id: item.socialMedia,
            link: item?.socialMediaLink,
            isHidden: item?.isHidden,
          })
        );
        const mergedList = state.athleteSocialMediaList?.map((item) => {
          const fromBackend = updatedFetchedList?.find(
            (each) => each?.id === item?.id
          );

          return {
            ...item,
            link: fromBackend?.link || "",
            isEnable: !fromBackend?.isHidden,
          };
        });

        state.athleteSocialMediaList = mergedList;
      })
      .addCase(fetchAthleteSocialMediaLinks.rejected, (state, action) => {
        state.apiStatus = "fetchAthleteSocialMFailed";
        state.error = action.payload as string;
      })
      .addCase(postAthleteQuickLinks.pending, (state) => {
        state.apiStatus = "quickLinksPending";
      })
      .addCase(postAthleteQuickLinks.fulfilled, (state) => {
        state.apiStatus = "postQuickLinksSuccess";
        toast.success("Quick links updated successfully!");
      })
      .addCase(postAthleteQuickLinks.rejected, (state, action) => {
        state.apiStatus = "postQuickLinksFailed";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to update quick links"
        );
      })
      .addCase(fetchAthleteQuickLinks.pending, (state) => {
        state.apiStatus = "fetchLinkPending";
      })
      .addCase(fetchAthleteQuickLinks.fulfilled, (state, action) => {
        state.apiStatus = "fetchLinkSuccess";
        const formattedList =
          action.payload?.length > 0 &&
          action.payload?.map((each) => ({
            id: each?.id,
            title: each?.videoTitle,
            link: each?.videoLink,
            isEditable: false,
          }));
        state.athleteQuickLinksList = formattedList;
      })
      .addCase(fetchAthleteQuickLinks.rejected, (state, action) => {
        state.apiStatus = "fetchLinkFailed";
        state.error = action.payload as string;
      })
      .addCase(putAthleteQuickLink.pending, (state) => {
        state.apiStatus = "quickLinksPending";
      })
      .addCase(putAthleteQuickLink.fulfilled, (state) => {
        state.apiStatus = "putQuickLinksSuccess";
        toast.success("Quick links updated successfully!");
      })
      .addCase(putAthleteQuickLink.rejected, (state, action) => {
        state.apiStatus = "putQuickLinksFailed";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to update quick links"
        );
      })
      .addCase(deleteAthleteQuickLink.pending, (state) => {
        state.apiStatus = "deleteQuickLinksPending";
      })
      .addCase(deleteAthleteQuickLink.fulfilled, (state) => {
        state.apiStatus = "deleteQuickLinksSuccess";
        toast.success("Quick link deleted successfully!");
      })
      .addCase(deleteAthleteQuickLink.rejected, (state, action) => {
        state.apiStatus = "deleteQuickLinksFailed";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to delete quick links"
        );
      })
      .addCase(postAthleteGallery.pending, (state) => {
        state.apiStatus = "galleryPending";
      })
      .addCase(postAthleteGallery.fulfilled, (state) => {
        state.apiStatus = "gallerySuccess";
        toast.success("Gallery item added successfully!");
      })
      .addCase(postAthleteGallery.rejected, (state, action) => {
        state.apiStatus = "galleryReject";
        state.error = action.payload as string;
        toast.error("Failed to add gallery item");
      })
      .addCase(fetchAthleteGallery.pending, (state, action) => {
        state.apiStatus = "fetchGalleryPending";
      })
      .addCase(fetchAthleteGallery.fulfilled, (state, action) => {
        state.apiStatus = "fetchGallerySuccess";
        const formattedData = action.payload?.map((each) => ({
          id: each?.id,
          title: each?.description,
          image: each?.fileLocation,
        }));
        state.athleteGalleryList = formattedData;
      })
      .addCase(fetchAthleteGallery.rejected, (state, action) => {
        state.apiStatus = "fetchGalleryFailed";
        state.error = action.payload as string;
      })
      .addCase(deleteAthleteGalleryItem.pending, (state) => {
        state.apiStatus = "deleteGalleryPending";
      })
      .addCase(deleteAthleteGalleryItem.fulfilled, (state) => {
        state.apiStatus = "deleteGallerySuccess";
        toast.success("Gallery deleted successfully!");
      })
      .addCase(deleteAthleteGalleryItem.rejected, (state, action) => {
        state.apiStatus = "deleteGalleryReject";
        state.error = action.payload as string;
        toast.error((action.payload as string) || "Failed to delete gallery");
      })
      .addCase(putAthletePhysStats.pending, (state) => {
        state.apiStatus = "physStatsPending";
      })
      .addCase(putAthletePhysStats.fulfilled, (state) => {
        state.apiStatus = "physStatsSuccess";
        toast.success("Physical Stats updated successfully!");
      })
      .addCase(putAthletePhysStats.rejected, (state, action) => {
        state.apiStatus = "physStatsReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to update physical stats"
        );
      })
      .addCase(fetchAthletePhysStats.pending, (state) => {
        state.apiStatus = "fetchPhyStatPending";
      })
      .addCase(fetchAthletePhysStats.fulfilled, (state, action) => {
        state.apiStatus = "fetchPhyStatSuccess";
        state.physStats = {
          hFeet: action.payload?.heightInFeet,
          hInches: action.payload?.heightInches,
          weight: action?.payload?.weightInLbs,
        };
        state.physicalStatsToggle = !action?.payload?.isHidden;
        state.physStatsId = action?.payload?.id;
      })
      .addCase(fetchAthletePhysStats.rejected, (state, action) => {
        state.apiStatus = "fetchPhyStatFailed";
        state.error = action.payload as string;
      })
      .addCase(postAthleteLearningHighLight.pending, (state) => {
        state.apiStatus = "learingPending";
      })
      .addCase(postAthleteLearningHighLight.fulfilled, (state) => {
        state.apiStatus = "learingSuccess";
        toast.success("Learning details updated successfully!");
      })
      .addCase(postAthleteLearningHighLight.rejected, (state, action) => {
        state.apiStatus = "learingReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to update learning details"
        );
      })
      .addCase(fetchAthleteLearningHighLights.pending, (state) => {
        state.apiStatus = "fetchLearningPending";
      })
      .addCase(fetchAthleteLearningHighLights.fulfilled, (state, action) => {
        state.apiStatus = "fetchLearningSuccess";
        if (action.payload) {
          const {
            academicMmt,
            actScore,
            currentSchoolName,
            gpa,
            gradYear,
            moreAbtOverallProgress,
            overallProgress,
            satScore,
            yrInSchool,
            topicStrength,
            school,
          } = action.payload;
          const formattedData = {
            currentSchoolName: { value: school?.id, label: school?.schoolName },
            otherSchoolName: currentSchoolName,
            graduationYear: gradYear,
            grade: yrInSchool,
            overallAcademic: overallProgress,
            gpa: gpa,
            sat: satScore,
            act: actScore,
            progress: moreAbtOverallProgress,
            academicMmt: academicMmt,
          };
          state.athleteLearning = formattedData;
          state.selectedLearingTopicsList = topicStrength?.map((each) => ({
            value: each?.id,
            label: each?.topicLabel,
          }));
        }
      })
      .addCase(fetchAthleteLearningHighLights.rejected, (state, action) => {
        state.apiStatus = "fetchLearningFailed";
        state.error = action.payload as string;
        state.athleteLearning = null;
        state.selectedLearingTopicsList = [];
      })
      .addCase(postAthleteAchievement.pending, (state) => {
        state.apiStatus = "achievementPending";
      })
      .addCase(postAthleteAchievement.fulfilled, (state) => {
        state.apiStatus = "achievementSuccess";
        toast.success("Achievement added successfully!");
      })
      .addCase(postAthleteAchievement.rejected, (state, action) => {
        state.apiStatus = "achievementReject";
        state.error = action.payload as string;
        toast.error((action.payload as string) || "Failed to add achievement");
      })
      .addCase(fetchAthleteAchievement.pending, (state) => {
        state.apiStatus = "fetchAchievementsPending";
      })
      .addCase(fetchAthleteAchievement.fulfilled, (state, action) => {
        state.apiStatus = "fetchAchievementsSuccess";
        if (action.payload?.length > 0) {
          const formattedList = action.payload?.map((each) => ({
            id: each?.id,
            title: each?.achievementTitle,
            date: each?.achievementDate,
            link: each?.achievementLink,
            tags: each?.tags?.map((each) => ({
              value: each?.id,
              label: each?.value,
            })),
            blurb: each?.achievementDesc,
            file: each?.s3FileLink,
          }));
          state.addedAchievementsList = formattedList;
        }
      })
      .addCase(fetchAthleteAchievement.rejected, (state, action) => {
        state.apiStatus = "fetchAchievementsFailed";
        state.error = action.payload as string;
      })
      .addCase(putAthleteAchievements.pending, (state) => {
        state.apiStatus = "achievementPending";
      })
      .addCase(putAthleteAchievements.fulfilled, (state) => {
        state.apiStatus = "achievementSuccess";
        toast.success("Achievement updated successfully!");
      })
      .addCase(putAthleteAchievements.rejected, (state, action) => {
        state.apiStatus = "achievementReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to update achievement"
        );
      })
      .addCase(deleteAthleteAchievements.pending, (state) => {
        state.apiStatus = "deleteAchievementPending";
      })
      .addCase(deleteAthleteAchievements.fulfilled, (state) => {
        state.apiStatus = "achievementSuccess";
        toast.success("Achievement deleted successfully!");
      })
      .addCase(deleteAthleteAchievements.rejected, (state, action) => {
        state.apiStatus = "achievementReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to delete achievement"
        );
      })
      .addCase(fetchAthleteAllSports.pending, (state) => {
        state.apiStatus = "fetchAllSportsPending";
      })
      .addCase(fetchAthleteAllSports.fulfilled, (state, action) => {
        state.apiStatus = "fetchAllSportsSuccess";
        if (action.payload?.length > 0) {
          const formattedList = action.payload?.map((each) => ({
            id: each?.id,
            isPrimary: each?.primarySportFlag === "Y",
            sportName: each?.sportName,
            sportId: each?.sportId,
            sportLevel: each?.levels?.length
              ? each?.levels?.[0]?.levelName
              : "",
            specilities: each?.specialities?.length
              ? each?.specialities?.map((each) => ({
                  id: each?.specialityId,
                  specilityName: each?.specialityName,
                }))
              : [],
            isEditable: false,
          }));
          state.mySportsList = formattedList;
        }
      })
      .addCase(fetchAthleteAllSports.rejected, (state, action) => {
        state.apiStatus = "fetchAllSportsFailed";
        state.error = action.payload as string;
      })
      .addCase(putAthleteGrowthInterests.pending, (state) => {
        state.apiStatus = "putGrowthPending";
      })
      .addCase(putAthleteGrowthInterests.fulfilled, (state, action) => {
        state.apiStatus = "putGrowthSuccess";
        toast.success("Development interests are updated successfully!");
      })
      .addCase(putAthleteGrowthInterests.rejected, (state, action) => {
        state.apiStatus = "putGrowthFailed";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to update development interests"
        );
      })
      .addCase(fetchAthleteGrowthInterests.fulfilled, (state, action) => {
        state.apiStatus = "";
        const formattedList = action.payload?.interests?.map((each) => ({
          value: each?.id,
          label: each?.name,
        }));
        state.achieveOrAccomplish = action.payload?.achievements;
        state.selectedGrowthList = formattedList;
      })
      .addCase(fetchAthleteGrowthInterests.rejected, (state, action) => {
        state.apiStatus = "";
        state.error = action.payload as string;
      })
      .addCase(putAthleteOpenSession.pending, (state) => {
        state.apiStatus = "putGrowthPending";
      })
      .addCase(putAthleteOpenSession.fulfilled, (state, action) => {
        state.apiStatus = "putGrowthSuccess";
        toast.success("Virtual session updated successfully!");
      })
      .addCase(putAthleteOpenSession.rejected, (state, action) => {
        state.apiStatus = "putGrowthFailed";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to update virtual session"
        );
      })
      .addCase(fetchAthleteOpenSession.fulfilled, (state, action) => {
        state.apiStatus = "";
        state.openVirtualSession = action.payload?.openToVirtualSession === "Y";
      })
      .addCase(fetchAthleteOpenSession.rejected, (state, action) => {
        state.apiStatus = "";
        state.error = action.payload as string;
      })
      .addCase(putAthleteToggleSections.pending, (state) => {
        state.apiStatus = "";
      })
      .addCase(putAthleteToggleSections.fulfilled, (state, action) => {
        state.apiStatus = "";
        toast.success("Profile features hide/unhide updated successfully!");
      })
      .addCase(putAthleteToggleSections.rejected, (state, action) => {
        state.apiStatus = "";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) ||
            "Failed to update profile features hide/unhide"
        );
      })
      .addCase(fetchAthleteToggleSections.fulfilled, (state, action) => {
        state.apiStatus = "";
        if (action?.payload) {
          const {
            achievements,
            allowCurrentSchoolToView,
            profile,
            socialMedia,
            quickLinks,
            physicalStats,
            parentGuardian,
            gallery,
          } = action?.payload;
          state.toggleSchoolView = allowCurrentSchoolToView;
          state.profileToggle = profile;
          state.toggleAchievements = achievements;
          state.toggleContactSection = parentGuardian;
          state.toggleGallery = gallery;
          state.socialMediaToggle = socialMedia;
          state.quickLinksToggle = quickLinks;
          state.physicalStatsToggle = physicalStats;
        }
      })
      .addCase(fetchAthleteToggleSections.rejected, (state, action) => {
        state.apiStatus = "";
        state.error = action.payload as string;
      })
      .addCase(putAthleteParentGuardian.pending, (state) => {
        state.apiStatus = "parentGuardianPending";
      })
      .addCase(putAthleteParentGuardian.fulfilled, (state, action) => {
        state.apiStatus = "parentGuardianSuccess";
        toast.success("Parent/Guardian details updated successfully!");
      })
      .addCase(putAthleteParentGuardian.rejected, (state, action) => {
        state.apiStatus = "parentGuardianFailed";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) ||
            "Failed to update Parent/Guardian details"
        );
      })
      .addCase(fetchAthleteParentGuardian.fulfilled, (state, action) => {
        state.apiStatus = "";
        if (action.payload) {
          const {
            parentFirstName,
            parentLastName,
            parentPhone,
            parentEmail,
            isHiddenMobile,
            isHiddenEmail,
            lastTermsAcceptedDt,
            makeProfilePublic,
            primaryParentReln,
            isHiddenPrimaryParentReln,
          } = action.payload;
          const formattedData = {
            firstName: parentFirstName,
            lastName: parentLastName,
            phone: parentPhone,
            togglePhone: !isHiddenMobile,
            email: parentEmail,
            toggleEmail: !isHiddenEmail,
            relationship: primaryParentReln,
            toggleRelation: !isHiddenPrimaryParentReln,
            searchMyProfileCA: !makeProfilePublic,
            lastTermsAcceptedDt: lastTermsAcceptedDt,
          };
          state.parentGuardianData = formattedData;
        }
      })
      .addCase(fetchAthleteParentGuardian.rejected, (state, action) => {
        state.apiStatus = "";
        state.error = action.payload as string;
      })
      .addCase(putAthleteLocations.pending, (state) => {
        state.apiStatus = "locationPending";
      })
      .addCase(putAthleteLocations.fulfilled, (state, action) => {
        state.apiStatus = "locationSuccess";
        toast.success("Preferred locations added successfully!");
      })
      .addCase(putAthleteLocations.rejected, (state, action) => {
        state.apiStatus = "locationFailed";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to add preferred locations"
        );
      })
      .addCase(fetchAthleteLocations.pending, (state) => {
        state.apiStatus = "fetchLocPending";
      })
      .addCase(fetchAthleteLocations.fulfilled, (state, action) => {
        state.apiStatus = "fetchLocSuccess";
        state.athleteAddedStateLocationsList = action.payload?.length
          ? action.payload
          : [];
      })
      .addCase(fetchAthleteLocations.rejected, (state, action) => {
        state.apiStatus = "fetchLocFailed";
        state.error = action.payload as string;
        state.athleteAddedStateLocationsList = [];
      })
      .addCase(deleteAthleteLocations.pending, (state) => {
        state.apiStatus = "delLocationPending";
      })
      .addCase(deleteAthleteLocations.fulfilled, (state, action) => {
        state.apiStatus = "delLocationSuccess";
        toast.success("Location deleted successfully!");
      })
      .addCase(deleteAthleteLocations.rejected, (state, action) => {
        state.apiStatus = "delLocationFailed";
        state.error = action.payload as string;
        toast.error((action.payload as string) || "Failed to delete location");
      });
  },
});

export const { handleUpdateUserInput } = athleteProfileSlice.actions;
export default athleteProfileSlice.reducer;
