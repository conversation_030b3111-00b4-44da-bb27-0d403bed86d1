"use client";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import parse from "html-react-parser";
import {
  titleParseOptions,
  shortDescriptionParseOptions,
  fileTitleParseOptions,
  fileDescriptionParseOptions,
} from "@/utils/parseOptions";
const UnlockPotential = () => {
  const { unlockSection, unlockImages, loading } = useSelector(
    (state: RootState) => state.homeCMS
  );

  if (loading) return <div className="text-white text-center">Loading...</div>;
  if (!unlockSection) return null;

  return (
    <section className="py-5 bg-[#0D1D3A] px-3 sm:px-8 md:px-16 lg:px-36 xl:px-56">
      <div className="container mx-auto">
        <div className="text-center mb-8">
          <h2>{parse(unlockSection.title || "", titleParseOptions)}</h2>
          <div className="max-w-2xl mx-auto mt-4">
            {parse(
              unlockSection.shortDescription || "",
              shortDescriptionParseOptions
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {unlockImages.map((img) => (
            <div
              key={img.id}
              className="group relative h-96 cursor-pointer overflow-hidden rounded-3xl shadow-xl border border-white/10 transition-all duration-500 hover:shadow-2xl"
            >
              {/* Background Image */}
              <img
                src={img.fileLocation}
                alt="Unlock Image"
                className="w-full h-full object-cover transform transition-transform duration-700 group-hover:scale-105"
              />

              {/* Shining effect */}
              <div className="absolute inset-0 z-10 pointer-events-none">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
              </div>

              {/* Overlay + Text */}
              <div className="absolute inset-0 bg-black bg-opacity-60 flex flex-col justify-center items-center text-center opacity-0 group-hover:opacity-100 transition-opacity duration-500 p-6 z-20">
                <h3 className="mb-3">
                  {parse(img.fileTitle || "", fileTitleParseOptions)}
                </h3>
                <div>
                  {parse(
                    img.fileDescription || "",
                    fileDescriptionParseOptions
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default UnlockPotential;
