import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useTokenValues } from "./useTokenValues";

const useRoleBasedAccess = (allowedRoles: number[]) => {
  const router = useRouter();
  const { userId, roleId } = useTokenValues();

  useEffect(() => {
    if (!userId) {
      return;
    }

    if (!allowedRoles.includes(roleId!)) {
      router.push("/404");
    }
  }, [roleId, allowedRoles, router]);

  return { userId };
};

export default useRoleBasedAccess;
