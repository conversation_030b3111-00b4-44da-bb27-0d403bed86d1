import { useHandleAthleteSectionExpose } from "@/hooks/useAthleteExposeSections"
import { AppDispatch, RootState } from "@/store"
import { fetchAthleteToggleSections } from "@/store/slices/athlete/athleteProfileSlice"
import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Switch } from "../ui/switch"

const SchoolViewProfile = () => {
    const { toggleSchoolView } = useSelector((state: RootState) => state.athleteProfile)
    const dispatch = useDispatch<AppDispatch>()
    const { handleToggleSections } = useHandleAthleteSectionExpose()

    useEffect(() => {
        dispatch(fetchAthleteToggleSections())
    }, [dispatch])

    const listOfSections = [
        {
            id: 'name',
            label: 'Athlete Name'
        },
        {
            id: 'age',
            label: 'Age Group'
        },
        {
            id: 'grade',
            label: 'Grade'
        },
        {
            id: 'graduationYear',
            label: 'Class Of (Graduation Year)'
        },
        {
            id: 'sportInfo',
            label: 'Sport Name, Level, and Position'
        },
    ]

    return (
        <>
            <div className="bg-slate-100 rounded-lg p-4 flex flex-col gap-5">
                <div className="flex items-center justify-center gap-2">
                    <h3 className="text-xl font-bold text-wrap text-center">Allow My Current School to View My Profile</h3>
                    <Switch
                        checked={toggleSchoolView}
                        onCheckedChange={(checked) => handleToggleSections('allowCurrentSchoolToView', checked)}
                    />
                </div>

                {toggleSchoolView ?
                    <>
                        <div className="w-full text-wrap">
                            <p>If a current school is selected from the dropdown and profile
                                sharing is enabled, and if that school is registered on Connect
                                Athlete, authorized school staff will be able to view limited basic
                                information. This gives the school helpful context about the athlete’s
                                background while keeping the full profile private.</p>
                        </div>

                        <ul className="list-disc ml-10">
                            {listOfSections?.map(each =>
                                <li key={each?.id}>{each.label}</li>
                            )}
                        </ul>
                    </> : null}
            </div>
        </>
    )
}
export default SchoolViewProfile