'use client'
import SearchInput from "@/components/common/SearchInput"
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"

const CoachFilterPanel = () => {
    return (
        <>
            <div className="bg-primary rounded-lg p-4 flex flex-col gap-8 sticky top-20">
                <div className="flex flex-col gap-1">
                    <Label className="text-white font-semibold">Sport</Label>
                    <SearchInput
                        list={[]}
                        name="coachSport"
                        value={null}
                        onChange={() => { }}
                        className="w-full"
                        placeholder="Select Sport"
                    />
                </div>

                <div className="flex flex-col gap-1">
                    <Label className="text-white font-semibold">Coach Type</Label>
                    <SearchInput
                        list={[]}
                        name="coachType"
                        value={null}
                        onChange={() => { }}
                        className="w-full"
                        placeholder="Select Type"
                    />
                </div>

                <div className="flex flex-col gap-1">
                    <Accordion type="single" collapsible>
                        <AccordionItem value="item-1">
                            <AccordionTrigger className="underline-none text-white">Sports Related More Details</AccordionTrigger>
                            <AccordionContent className="flex flex-col gap-8">
                                <div className="flex flex-col gap-1">
                                    <Label className="text-white font-semibold">Level</Label>
                                    <SearchInput
                                        list={[]}
                                        name="coachLevel"
                                        value={null}
                                        onChange={() => { }}
                                        className="w-full"
                                        placeholder="Select Level"
                                    />
                                </div>

                                <div className="flex flex-col gap-1">
                                    <Label className="text-white font-semibold">Speciality</Label>
                                    <SearchInput
                                        list={[]}
                                        name="coachSpeciality"
                                        value={null}
                                        onChange={() => { }}
                                        className="w-full"
                                        placeholder="Select Speciality"
                                    />
                                </div>
                            </AccordionContent>
                        </AccordionItem>
                    </Accordion>
                </div>

                <Separator />

                <div className="flex flex-col gap-1">
                    <Label className="text-white font-semibold">State</Label>
                    <SearchInput
                        list={[]}
                        name="coachState"
                        value={null}
                        onChange={() => { }}
                        className="w-full"
                        placeholder="Select State"
                    />
                </div>

                <div className="flex flex-col gap-1">
                    <Accordion type="single" collapsible>
                        <AccordionItem value="item-1">
                            <AccordionTrigger className="underline-none text-white">Location Related More Details</AccordionTrigger>
                            <AccordionContent className="flex flex-col gap-8">
                                <div className="flex flex-col gap-1">
                                    <Label className="text-white font-semibold">Counties</Label>
                                    <SearchInput
                                        list={[]}
                                        name="coachCounties"
                                        value={null}
                                        onChange={() => { }}
                                        className="w-full"
                                        placeholder="Select Counties"
                                    />
                                </div>

                                <div className="flex flex-col gap-1">
                                    <Label className="text-white font-semibold">Locations</Label>
                                    <SearchInput
                                        list={[]}
                                        name="coachLocations"
                                        value={null}
                                        onChange={() => { }}
                                        className="w-full"
                                        placeholder="Select Locations"
                                    />
                                </div>
                            </AccordionContent>
                        </AccordionItem>
                    </Accordion>
                </div>

                <Separator />

                <div className="flex flex-col gap-1">
                    <Accordion type="single" collapsible>
                        <AccordionItem value="item-1">
                            <AccordionTrigger className="underline-none text-white">More</AccordionTrigger>
                            <AccordionContent className="flex flex-col gap-8">
                                <div className="flex flex-col gap-1">
                                    <Label className="text-white font-semibold">Age Group</Label>
                                    <SearchInput
                                        list={[]}
                                        name="coachAgeGroup"
                                        value={null}
                                        onChange={() => { }}
                                        className="w-full"
                                        placeholder="Select Age Group"
                                    />
                                </div>

                                <div className="flex flex-col gap-1">
                                    <Label className="text-white font-semibold">Gender</Label>
                                    <SearchInput
                                        list={[]}
                                        name="coachGender"
                                        value={null}
                                        onChange={() => { }}
                                        className="w-full"
                                        placeholder="Select Gender"
                                    />
                                </div>

                                <div className="flex flex-col gap-1">
                                    <Label className="text-white font-semibold">Coach Background</Label>
                                    <SearchInput
                                        list={[]}
                                        name="coachBackground"
                                        value={null}
                                        onChange={() => { }}
                                        className="w-full"
                                        placeholder="Select Coach Background"
                                    />
                                </div>

                                <div className="flex flex-col gap-1">
                                    <Label className="text-white font-semibold">Training/Offering</Label>
                                    <SearchInput
                                        list={[]}
                                        name="coachTraining"
                                        value={null}
                                        onChange={() => { }}
                                        className="w-full"
                                        placeholder="Select Training/Offering"
                                    />
                                </div>

                                <div className="flex flex-col gap-1">
                                    <Label className="text-white font-semibold">Coach Intent</Label>
                                    <SearchInput
                                        list={[]}
                                        name="coachIntent"
                                        value={null}
                                        onChange={() => { }}
                                        className="w-full"
                                        placeholder="Select Coach Intent"
                                    />
                                </div>

                                <div className="flex flex-col gap-1">
                                    <Label className="text-white font-semibold">Avaialability</Label>
                                    <SearchInput
                                        list={[]}
                                        name="coachAvaialability"
                                        value={null}
                                        onChange={() => { }}
                                        className="w-full"
                                        placeholder="Select Avaialability"
                                    />
                                </div>
                            </AccordionContent>
                        </AccordionItem>
                    </Accordion>
                </div>
            </div>
        </>
    )
}
export default CoachFilterPanel