"use client";
import { useEffect, useRef, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import type { AppDispatch } from "@/store";
import { RootState } from "@/store";
import { X } from "lucide-react";
import { useRouter } from "next/navigation";
import {
  setFormUserType,
  updateFormData,
  setErrors,
  clearErrors,
  setAgreed,
  resetForm,
  fetchRoleId,
  signupUser,
  verifyOtp,
} from "@/store/slices/auth/registerSlice";
import { Button } from "@/components/ui/button";
import { Input } from "../ui/input";
import Link from "next/link";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import Login from "./Login";

const nameRegex = /^[A-Za-z ]{2,50}$/;
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const phoneRegex = /^[0-9]{10}$/;

export default function RegistrationForm() {
  const dispatch = useDispatch<AppDispatch>();
  const { formUserType, formData, errors, agreed, roleId, userId } =
    useSelector((state: RootState) => state.register);

  const [showPopup, setShowPopup] = useState(false);
  const [otpDigits, setOtpDigits] = useState(Array(6).fill(""));
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [otpError, setOtpError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [confirmPasswordError, setConfirmPasswordError] = useState("");
  const otpInputRefs = useRef<HTMLInputElement[]>([]);
  const [loading, setLoading] = useState(false);
  const [showVerification, setShowVerification] = useState(false);
  const [termsData, setTermsData] = useState<{
    title: string;
    content: string;
  } | null>(null);
  const [openTermsDialog, setOpenTermsDialog] = useState(false);
  const [activeTab, setActiveTab] = useState<"login" | "signup">("signup");
  const [showLoginPopup, setShowLoginPopup] = useState(false);
  const router = useRouter();
  const signupContents = [
    {
      role: "athlete",
      label: "Athletes / Student",
      icon: "/landing-page-icons/athlete-icon.png",
      cta: "Create Athlete’s / Student's Free Profile →",
      subtext:
        "Build your athlete’s / student's profile, track their journey, and connect with coaches, programs, and resources that support their growth.",
    },
    {
      role: "coach",
      label: "Coaches",
      icon: "/landing-page-icons/sport-icon.png",
      cta: "Join as a Coach →",
      subtext:
        "Support your student-athletes by promoting programs, sharing opportunities, and becoming part of a platform built for equity and access.",
    },
    {
      role: "business",
      label: "Sports Businesses / Organizations",
      icon: "/landing-page-icons/business-icon.png",
      cta: "Sign up as a Sports Business /Organization →",
      subtext:
        "Showcase your programs, reach engaged families and coaches, and grow your presence within the Connect Athlete ecosystem.",
    },
  ];

  const handleOtpChange = (index: number, value: string) => {
    if (!/^\d?$/.test(value)) return;
    const updated = [...otpDigits];
    updated[index] = value;
    setOtpDigits(updated);
    if (value && index < otpDigits.length - 1) {
      otpInputRefs.current[index + 1]?.focus();
    }
  };

  const renderTermsAndConditions = () => {
    const getTermsLink = () => {
      if (!formUserType) return "#";
      return `/terms/${formUserType}`; // Adjust this URL path based on your actual routing
    };

    const renderLink = (text: string) => (
      <a
        href={getTermsLink()}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-400 underline cursor-pointer"
      >
        {text}
      </a>
    );

    switch (formUserType) {
      case "athlete":
        return (
          <p>
            I am 18 years or older, or the parent/legal guardian of the athlete.
            I have read and agree to the{" "}
            {renderLink(
              "Connect Athlete Terms & Conditions for Athletes / Students and Parents"
            )}
            .
          </p>
        );
      case "coach":
        return (
          <p>
            I have read and agree to the{" "}
            {renderLink("Connect Athlete Coach Terms & Conditions")}.
          </p>
        );
      case "business":
        return (
          <p>
            I am an authorized representative of this business/organization. I
            have read and agree to the{" "}
            {renderLink(
              "Connect Athlete Terms & Conditions for Sports Businesses/Organizations"
            )}
            .
          </p>
        );
      default:
        return null;
    }
  };

  const inputField = (
    label: string,
    name: string,
    placeholder: string,
    optional = false
  ) => (
    <div className="w-full mb-4">
      <label
        htmlFor={name}
        className="block text-sm font-medium text-gray-300 mb-1"
      >
        {label}
      </label>
      <Input
        id={name}
        name={name}
        placeholder={placeholder}
        value={formData[name] || ""}
        onChange={handleChange}
        className="w-full bg-[#1f2937] text-white placeholder-gray-400 border border-gray-600"
      />
      {!optional && errors[name] && (
        <p className="text-red-500 text-sm mt-1">{errors[name]}</p>
      )}
    </div>
  );

  const renderFields = () => {
    switch (formUserType) {
      case "athlete":
        return (
          <div className="flex flex-col gap-4">
            {inputField(
              "Athlete / Student Preferred Name",
              "name",
              "Enter Athlete / Student Preferred Name"
            )}
            {inputField(
              "Parent/Guardian First Name",
              "firstName",
              "Enter Parent/Guardian First Name"
            )}
            {inputField(
              "Parent/Guardian Last Name (Optional)",
              "lastName",
              "Enter Parent/Guardian Last Name",
              true
            )}
            {inputField(
              "Parent/Guardian Email",
              "email",
              "Enter Parent/Guardian Email"
            )}
            {inputField(
              "Parent/Guardian Mobile Number",
              "mobile",
              "Enter 10-digit Parent/Guardian Mobile Number"
            )}
          </div>
        );
      case "coach":
        return (
          <div className="flex flex-col gap-4">
            {inputField("First Name", "firstName", "Enter First Name")}
            {inputField(
              "Last Name (Optional)",
              "lastName",
              "Enter Last Name",
              true
            )}
            {inputField("Email", "email", "Enter Email")}
            {inputField(
              "Mobile Number",
              "mobile",
              "Enter 10-digit Mobile Number"
            )}
          </div>
        );
      case "business":
        return (
          <div className="flex flex-col gap-4">
            {inputField("Business Name", "name", "Enter Business Name")}
            {inputField(
              "Contact First Name",
              "firstName",
              "Enter Contact First Name"
            )}
            {inputField(
              "Contact Last Name (Optional)",
              "lastName",
              "Enter Contact Last Name",
              true
            )}
            {inputField("Email", "email", "Enter Email")}
            {inputField(
              "Mobile Number",
              "mobile",
              "Enter 10-digit Mobile Number"
            )}
          </div>
        );
      default:
        return null;
    }
  };

  const fetchTermsAndConditions = async (userType: string) => {
    try {
      const response = await fetch(`/api/get-terms?type=${userType}`);
      const result = await response.json();
      if (result.status === 200) {
        setTermsData({
          title: result.data.title,
          content: result.data.content,
        });
        setOpenTermsDialog(true);
      }
    } catch (error) {
      console.error("Failed to fetch terms", error);
    }
  };

  const handleOtpKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === "Backspace" && !otpDigits[index] && index > 0) {
      otpInputRefs.current[index - 1]?.focus();
    }
  };

  const getOtpValue = () => otpDigits.join("");

  const handleRoleSelection = async (role: any) => {
    dispatch(setFormUserType(role));
    await dispatch(fetchRoleId(role));
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    dispatch(updateFormData({ name, value }));

    if (name === "email") {
      dispatch(
        setErrors({
          ...errors,
          [name]: emailRegex.test(value) ? "" : "Invalid email format",
        })
      );
    }

    if (name === "mobile") {
      dispatch(
        setErrors({
          ...errors,
          [name]: phoneRegex.test(value) ? "" : "Enter a valid 10-digit number",
        })
      );
    }
  };

  const validateFields = (fields: string[]) => {
    const newErrors: { [key: string]: string } = {};
    fields.forEach((field) => {
      const value = formData[field] || "";
      if (!value.trim()) {
        newErrors[field] = "This field is required";
      } else {
        if (
          field.includes("Name") ||
          field.includes("First") ||
          field.includes("Last")
        ) {
          if (!nameRegex.test(value))
            newErrors[field] = "Only letters and spaces allowed (2–50 chars)";
        }
        if (field === "email" && !emailRegex.test(value)) {
          newErrors[field] = "Invalid email format";
        }
        if (field === "mobile" && !phoneRegex.test(value)) {
          newErrors[field] = "Enter a valid 10-digit number";
        }
      }
    });
    return newErrors;
  };

  useEffect(() => {
    if (formUserType) {
      dispatch(fetchRoleId(formUserType));
    }
  }, [formUserType]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    let requiredFields: string[] = [];
    switch (formUserType) {
      case "athlete":
        requiredFields = ["name", "firstName", "email", "mobile"];
        break;
      case "coach":
        requiredFields = ["firstName", "email", "mobile"];
        break;
      case "business":
        requiredFields = ["name", "firstName", "email", "mobile"];
        break;
    }
    const normalizedFormData = {
      ...formData,
      name:
        formUserType === "coach"
          ? formData.name || ""
          : formUserType === "business"
          ? formData.name || ""
          : formData.name || "",
    };
    const validationErrors = validateFields(requiredFields);
    if (Object.keys(validationErrors).length > 0) {
      dispatch(setErrors(validationErrors));
      return;
    }
    dispatch(clearErrors());
    setLoading(true);
    try {
      if (!roleId) throw new Error("Role ID not found");
      await dispatch(
        signupUser({
          roleType: formUserType!,
          roleId,
          formData: normalizedFormData,
        })
      ).unwrap();
      setShowVerification(true);
    } catch (error: any) {
      alert(error.message || "Something went wrong.");
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmRegistration = async () => {
    // Inside your component
    const otp = getOtpValue();
    let hasError = false;

    if (!otp || otp.length < 6) {
      setOtpError("Please enter the full 6-digit OTP");
      hasError = true;
    } else {
      setOtpError("");
    }

    const roleId = await dispatch(fetchRoleId(formUserType!)).unwrap();

    // Get userId from wherever you're storing it
    const userData: any = JSON.parse(localStorage.getItem("userInfo") || "{}");
    // const userId = userData?.id;

    if (!hasError && userId && roleId) {
      try {
        const response = await dispatch(
          verifyOtp({ userId, roleId, otp })
        ).unwrap();

        if (response?.status === 200) {
          // Save verified user info
          const user = response?.user;
          const profileData = response?.profileData;
          const token = response?.token;
          localStorage.setItem("verifiedUser", JSON.stringify(response?.data));
          localStorage.setItem("userInfo", JSON.stringify(user));
          localStorage.setItem("profileInfo", JSON.stringify(profileData));
          localStorage.setItem("token", token);
          localStorage.setItem("userId", user.id);
          localStorage.setItem("roleId", user.roleId);
          localStorage.setItem("profileId", profileData?.id);
          // Redirect based on roleId
          switch (roleId) {
            case 2:
              router.push("/athlete");
              break;
            case 3:
              router.push("/coach");
              break;
            case 4:
              router.push("/business");
              break;
            default:
              console.warn("Unknown roleId:", roleId);
              break;
          }
        } else {
          console.warn("Unexpected response from OTP verification:", response);
        }

        setTimeout(() => {
          dispatch(resetForm());
        }, 1000);
      } catch (error: any) {
        alert(error.message || "Something went wrong");
      }
    }
  };

  return (
    <section
      id="registration"
      className="bg-[#0D1D3A] text-white py-10 px-3 sm:px-8 md:px-16 lg:px-36 xl:px-56 w-full"
    >
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-6">
          Start Your Journey with Connect Athlete
        </h2>

        <p className="cms-text text-lg text-gray-300 leading-relaxed max-w-3xl mx-auto">
          Whether you're an athlete, coach, or partner—there’s a place for you
          here.
        </p>
      </div>
      <div className="w-full max-w-4xl mx-auto shadow-lg p-8 rounded-md border border-white/10 bg-white/5">
        {!formUserType && (
          <>
            <h2 className="text-2xl font-bold mb-6 text-center text-white">
              Sign up for free today
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
              {signupContents.map(({ role, label, icon, cta, subtext }) => (
                <div
                  key={role}
                  onClick={() => handleRoleSelection(role)}
                  className="cursor-pointer group flex flex-col items-start justify-start border border-white/10 rounded-lg p-6 hover:shadow-lg transition-shadow duration-300 bg-[#111827] hover:bg-[#1f2937]"
                >
                  <div className="flex items-center gap-3 mb-4">
                    <img
                      src={icon}
                      alt={`${label} Icon`}
                      className="w-12 h-12 object-contain group-hover:scale-105 transition-transform duration-300"
                    />
                    <span className="text-lg font-semibold text-white group-hover:text-orange-400">
                      {label}
                    </span>
                  </div>
                  <div className="text-sm text-white/80 mb-2 font-medium group-hover:text-white">
                    {cta}
                  </div>
                  <p className="text-sm text-gray-400 leading-snug">
                    {subtext}
                  </p>
                </div>
              ))}
            </div>
            <p className="text-center text-gray-400">
              No matter your role in the journey—we’re here to help you make an
              impact.
            </p>
          </>
        )}

        {formUserType && (
          <>
            <h2 className="text-2xl font-bold mb-8 text-left capitalize text-white">
              {formUserType} Sign up form
            </h2>
            {!showVerification && (
              <form onSubmit={handleSubmit} className="flex flex-col gap-4">
                {renderFields()}
                <div className="flex items-start mt-4">
                  <input
                    id="terms"
                    type="checkbox"
                    checked={agreed}
                    onChange={(e) => dispatch(setAgreed(e.target.checked))}
                    className="h-4 w-4 mt-1 mr-2 border-gray-300 rounded "
                  />
                  <label htmlFor="terms" className="text-sm text-gray-300">
                    {renderTermsAndConditions()}
                  </label>
                </div>
                <div className="flex justify-end gap-4 pt-6">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={() => dispatch(resetForm())}
                  >
                    Reset
                  </Button>
                  <Button
                    type="submit"
                    variant={"primaryGradient"}
                    disabled={!agreed}
                  >
                    Sign up
                  </Button>
                </div>
              </form>
            )}
            {showVerification && (
              <div className="mt-10 border-t border-white/10 pt-6">
                <h3 className="text-lg font-semibold mb-4 text-green-400">
                  Registration Successful!
                </h3>
                <p className="mb-4 text-gray-300">
                  Please enter the OTP sent to your email to verify your
                  account.
                </p>

                <div className="flex flex-col gap-4">
                  <div>
                    <label className="text-sm text-gray-400">Email</label>
                    <input
                      type="email"
                      value={formData.email || ""}
                      disabled
                      className="w-full mt-1 px-3 py-2 border rounded-md bg-gray-700 text-white cursor-not-allowed"
                    />
                  </div>

                  <div>
                    <label className="text-sm text-gray-400 mb-1 block">
                      Verify OTP
                    </label>
                    <div className="flex justify-between gap-2">
                      {otpDigits.map((digit, idx) => (
                        <input
                          key={idx}
                          ref={(el) => {
                            if (el) otpInputRefs.current[idx] = el;
                          }}
                          type="text"
                          maxLength={1}
                          inputMode="numeric"
                          value={digit}
                          onChange={(e) => handleOtpChange(idx, e.target.value)}
                          onKeyDown={(e) => handleOtpKeyDown(idx, e)}
                          className="w-10 h-12 text-center border rounded-md bg-gray-800 text-white text-xl focus:outline-none focus:ring-2 focus:ring-green-400"
                        />
                      ))}
                    </div>
                    {otpError && (
                      <p className="text-red-400 text-sm mt-1">{otpError}</p>
                    )}
                  </div>

                  <Button
                    variant={"primaryGradient"}
                    onClick={handleConfirmRegistration}
                    className="w-full mt-2"
                  >
                    Verify OTP & Sign In
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {showPopup && (
        <div className="fixed inset-0 z-50 bg-black/70 flex items-center justify-center">
          <div className="bg-[#1f2937] w-full max-w-md p-6 rounded-md shadow-lg relative text-white">
            <button
              onClick={() => setShowPopup(false)}
              className="absolute top-2 right-2 text-gray-300 hover:text-white text-xl"
            >
              <X />
            </button>

            <h3 className="text-xl font-semibold mb-4 text-center text-green-400">
              Registration Successful!
            </h3>
            <p className="mb-4 text-center text-gray-300">
              Please enter the OTP sent to your email and set your password to
              complete registration.
            </p>

            <div className="flex flex-col gap-4">
              <div>
                <label className="text-sm text-gray-400">Email</label>
                <input
                  type="email"
                  value={formData.email || ""}
                  disabled
                  className="w-full mt-1 px-3 py-2 border rounded-md bg-gray-700 text-white cursor-not-allowed"
                />
              </div>

              <div>
                <label className="text-sm text-gray-400 mb-1 block">
                  Verify OTP
                </label>
                <div className="flex justify-between gap-2">
                  {otpDigits.map((digit, idx) => (
                    <input
                      key={idx}
                      ref={(el) => {
                        if (el) otpInputRefs.current[idx] = el;
                      }}
                      type="text"
                      maxLength={1}
                      inputMode="numeric"
                      value={digit}
                      onChange={(e) => handleOtpChange(idx, e.target.value)}
                      onKeyDown={(e) => handleOtpKeyDown(idx, e)}
                      className="w-10 h-12 text-center border rounded-md bg-gray-800 text-white text-xl focus:outline-none focus:ring-2 focus:ring-green-400"
                    />
                  ))}
                </div>
                {otpError && (
                  <p className="text-red-400 text-sm mt-1">{otpError}</p>
                )}
              </div>

              <div>
                <label className="text-sm text-gray-400">Password</label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full mt-1 px-3 py-2 border rounded-md bg-gray-800 text-white"
                />
                {passwordError && (
                  <p className="text-red-400 text-sm mt-1">{passwordError}</p>
                )}
              </div>

              <div>
                <label className="text-sm text-gray-400">
                  Confirm Password
                </label>
                <input
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="w-full mt-1 px-3 py-2 border rounded-md bg-gray-800 text-white"
                />
                {confirmPasswordError && (
                  <p className="text-red-400 text-sm mt-1">
                    {confirmPasswordError}
                  </p>
                )}
              </div>

              <Button
                variant={"primaryGradient"}
                onClick={handleConfirmRegistration}
                className="w-full mt-2"
              >
                Confirm & Sign In
              </Button>
            </div>
          </div>
        </div>
      )}

      <Dialog open={openTermsDialog} onOpenChange={setOpenTermsDialog}>
        <DialogContent className="max-w-5xl w-full max-h-[90vh] overflow-y-auto p-0 bg-gray-900 text-white">
          {/* Close icon */}
          <DialogClose asChild>
            <button
              className="absolute top-4 right-4 text-white hover:text-gray-300 focus:outline-none"
              aria-label="Close"
            >
              <X className="w-5 h-5" />
            </button>
          </DialogClose>

          {/* Sticky header */}
          <div className="sticky top-0 z-10 bg-gray-900 p-6 border-b border-gray-700">
            <DialogHeader>
              <DialogTitle className="text-white text-md">
                Before proceeding, please read the Terms & Conditions using the link below.
              </DialogTitle>
              <DialogDescription className="text-gray-400 text-sm">
                Please read carefully before continuing.
              </DialogDescription>
            </DialogHeader>
          </div>

          {/* Scrollable content */}
          <div className="p-6">
            {termsData ? (
              <div
                className="prose prose-invert max-w-none"
                dangerouslySetInnerHTML={{
                  __html: termsData.title + termsData.content,
                }}
              />
            ) : (
              <p className="text-sm text-gray-400">Loading terms...</p>
            )}
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={showLoginPopup} onOpenChange={setShowLoginPopup}>
        <DialogContent className="max-w-md w-full bg-gray-900 text-white p-6 rounded-md">
          <DialogClose asChild>
            <button
              className="absolute top-4 right-4 text-white hover:text-gray-300 focus:outline-none"
              aria-label="Close"
            >
              <X className="w-5 h-5" />
            </button>
          </DialogClose>

          <DialogHeader>
            <DialogTitle className="text-xl text-white text-center mb-4">
              Login
            </DialogTitle>
          </DialogHeader>

          <Login defaultEmail={formData.email} openByDefault={true} />
        </DialogContent>
      </Dialog>
    </section>
  );
}
