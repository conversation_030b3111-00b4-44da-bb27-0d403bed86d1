'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    Card, CardAction, CardContent, Card<PERSON>ooter,
    CardHeader, CardTitle
} from "@/components/ui/card"
import {
    Select, SelectContent, SelectGroup, SelectItem,
    SelectLabel, SelectTrigger, SelectValue
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { format } from "date-fns"
import { Download, PencilLine, Trash2 } from "lucide-react"
import { useDispatch, useSelector } from "react-redux"
import AddCertifications from "./AddCertifications"

const CoachCertifications = () => {
    const {
        toggleCertification,
        coachAddedCertificatesList
    } = useSelector((state: RootState) => state.coachPro<PERSON>le)
    const { documentTypesList } = useSelector((state: RootState) => state.commonSlice)
    const dispatch = useDispatch()

    const handleOnChange = (name: string, checked: boolean) => {
        dispatch(handleCoachInputChange({ name, value: checked }))
    }

    const handleEditCertification = (id: number) => {
        const selectedItem = coachAddedCertificatesList?.find(each => each.id === id)
        dispatch(handleCoachInputChange({ name: 'certificatesData', value: selectedItem }))
        dispatch(handleCoachInputChange({ name: 'isAddCertificates', value: true }))
        dispatch(handleCoachInputChange({ name: 'isEditCertificate', value: true }))
    }

    const handleDeleteItem = (id: number) => {
        const updatedList = coachAddedCertificatesList?.filter(each => each.id !== id)
        dispatch(handleCoachInputChange({ name: 'coachAddedCertificatesList', value: updatedList }))
    }

    return (
        <div className="flex flex-col gap-8 bg-slate-100 rounded-lg p-4">
            <div className="flex items-center justify-center gap-2">
                <h2 className="font-bold text-xl text-center">
                    View Certifications, Licences, Awards, Referrals & Testimonials
                </h2>
                <Switch
                    name="toggleCertification"
                    checked={toggleCertification}
                    onCheckedChange={(checked) => handleOnChange("toggleCertification", checked)}
                />
            </div>

            {toggleCertification && (
                <>
                    <div className="flex items-center justify-center gap-3">
                        {coachAddedCertificatesList?.length ? <Select>
                            <SelectTrigger className="w-[250px]">
                                <SelectValue placeholder="Filter By Document Type" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectGroup>
                                    <SelectLabel>Document Types</SelectLabel>
                                    {documentTypesList?.map(type => (
                                        <SelectItem key={type.value} value={type?.value?.toString()}>
                                            {type.label.toUpperCase()}
                                        </SelectItem>
                                    ))}
                                </SelectGroup>
                            </SelectContent>
                        </Select> : null}
                        <div className="self-end">
                            <AddCertifications />
                        </div>
                    </div>

                    <div className="flex flex-col gap-8">
                        {coachAddedCertificatesList?.map(each => (
                            <Card className="py-3" key={each.id}>
                                <CardHeader>
                                    <CardTitle className="text-xl font-bold">{each.title}</CardTitle>
                                    <CardAction>
                                        <Button size="icon" variant="outline" onClick={() => handleEditCertification(each.id)}>
                                            <PencilLine />
                                        </Button>
                                    </CardAction>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div className="flex flex-col gap-1">
                                            <p>{each.documentType?.label}</p>
                                            <p>Uploaded On: {each.uploadedOn && format(new Date(each.uploadedOn), 'MMM dd, yyyy')}</p>
                                            <a
                                                href={each.documentLink}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-blue-600 underline"
                                            >
                                                {each.documentLink}
                                            </a>
                                        </div>
                                        {/* Need to edit for s3 file link */}
                                        {/* <div className="flex flex-col items-center gap-2">
                                            <TooltipProvider>
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <img
                                                            src={
                                                                each?.file?.type?.startsWith('image')
                                                                    ? '/img-picture.svg'
                                                                    : each?.file?.type?.startsWith('video')
                                                                        ? '/video.svg'
                                                                        : '/pdf-file.svg'
                                                            }
                                                            className="w-16 cursor-pointer"
                                                        />
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>{each.file?.name}</p>
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TooltipProvider>
                                            {each.file && (
                                                <a
                                                    href={URL.createObjectURL(each.file)}
                                                    download={each.file.name}
                                                    className="flex items-center gap-1"
                                                >
                                                    <Download className="w-4" />
                                                    Download
                                                </a>
                                            )}
                                        </div> */}
                                    </div>
                                </CardContent>
                                <CardFooter className="flex justify-end">
                                    <Button size="icon" variant="destructive" onClick={() => handleDeleteItem(each.id)}>
                                        <Trash2 />
                                    </Button>
                                </CardFooter>
                            </Card>
                        ))}
                    </div>
                </>
            )}
        </div>
    )
}

export default CoachCertifications
