'use client';
import ClientGuard from "@/components/ClientGuard";
import PremiumPlans from "@/components/premium/PremiumPlans";
import { useTokenValues } from "@/hooks/useTokenValues";
import { AppDispatch, RootState } from "@/store";
import { fetchTransactionsHistory } from "@/store/slices/premiumSlice";
import { ROLES } from "@/utils/constants";
import { format } from "date-fns";
import { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";

const AthletePremiumAccessPage = () => {
    const { transactionsList } = useSelector((state: RootState) => state.premium)
    const { isPremiumUser } = useTokenValues()
    const dispatch = useDispatch<AppDispatch>()

    useEffect(() => {
        dispatch(fetchTransactionsHistory())
    }, [dispatch])

    const isLatestTransaction = useMemo(() => {
        return transactionsList?.filter(each => each?.isLatest)?.[0]
    }, [transactionsList])

    return (
        <ClientGuard allowedRoles={[ROLES.ATHLETE]}>
            <>
                <div className="flex flex-col gap-5">
                    {isPremiumUser ?
                        <h2 className="text-primary text-2xl font-bold text-center">
                            Renew Your Premium Access Before <br />
                            {isLatestTransaction?.renewalDate && format(new Date(isLatestTransaction?.renewalDate), 'MMM dd, yyyy') || ' It Expires'}
                        </h2>
                        :
                        <h2 className="text-primary text-2xl font-bold text-center">Upgrade to Premium Access</h2>
                    }
                    <PremiumPlans />
                </div>
            </>
        </ClientGuard>
    )
}
export default AthletePremiumAccessPage