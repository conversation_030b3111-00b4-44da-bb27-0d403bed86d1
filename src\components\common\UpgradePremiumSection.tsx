'use client'
import { useTokenValues } from "@/hooks/useTokenValues"
import { ROLES } from "@/utils/constants"
import { useRouter } from "next/navigation"
import { WiStars } from "react-icons/wi"
import { <PERSON><PERSON> } from "../ui/button"

const UpgradePremiumSection = () => {
    const router = useRouter()
    const { roleId } = useTokenValues()

    const handleUpgradeToPremium = () => {
        if (roleId === ROLES.ATHLETE) {
            router.push(`/athlete/premium-plan`)
        }
    }

    return (
        <>
            <div className="flex flex-col items-center justify-center gap-1 text-yellow-500  bg-yellow-50 border border-dashed border-yellow-500 py-3 rounded-2xl">
                <p className="font-bold flex items-center gap-2"><WiStars className="text-yellow-500 text-3xl" size={22} /> Premium Users Only</p>
                <p>Upgrade to Premium to access this section.</p>
                <Button onClick={handleUpgradeToPremium} className="px-6 py-2 bg-yellow-500 text-white font-semibold rounded-full shadow-md hover:bg-yellow-600 transition-all">
                    Upgrade to Premium
                </Button>
            </div>
        </>
    )
}
export default UpgradePremiumSection