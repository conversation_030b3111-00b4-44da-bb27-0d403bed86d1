import React from "react";
import { PenLine, CalendarDays } from "lucide-react";

const dummyData = {
  studentName: "Varun",
  programTitle: "MENTAL WELLNESS 101",
  completionDate: "July 15, 2025",
  certificateTitle: "CERTIFICATE OF COMPLETION",
  certificateText:
    "awarded for exceptional participation and excellence in sport",
  issuedBy: "<PERSON><PERSON>",
  issuedByTitle: "Sports Coach",
  signedBy: "<PERSON><PERSON>",
  signedByTitle: "School Head",
  logo: "/logo.png",
  signature: "/signature.png",
  subTitle: "OF ACHIEVEMENT",
};

const ModernCertificate = () => {
  const {
    studentName,
    programTitle,
    completionDate,
    certificateTitle,
    subTitle,
    certificateText,
    issuedBy,
    issuedByTitle,
    signedBy,
    signedByTitle,
    logo,
    signature,
  } = dummyData;

  return (
    <div className="max-w-4xl mx-auto p-10 bg-gradient-to-br from-purple-700 via-purple-800 to-indigo-900 rounded-xl shadow-2xl relative overflow-hidden">
      <div className="bg-white rounded-lg p-10 relative z-10">
        <div className="text-center">
          {logo && <img src={logo} alt="Logo" className="h-14 mx-auto mb-4" />}

          <h1 className="text-4xl font-black text-gray-800 tracking-widest">
            {certificateTitle}
          </h1>
          <p className="text-sm font-semibold text-white bg-gradient-to-r from-pink-500 to-purple-600 inline-block px-4 py-1 rounded-full mt-2 tracking-wide shadow">
            {/* {subTitle} */}
          </p>

          <p className="text-gray-700 mt-6 text-lg">
            THIS CERTIFICATE IS PROUDLY PRESENTED FOR
          </p>
          {/* <p className="text-lg text-gray-700">HONORABLE ACHIEVEMENT TO</p> */}

          <h2 className="text-3xl font-bold italic text-purple-600 mt-4">
            {studentName}
          </h2>

          <p className="text-gray-500 mt-4 max-w-2xl mx-auto text-sm">
            {certificateText}
          </p>

          <h3 className="text-lg font-medium text-gray-700 mt-4">
            {programTitle}
          </h3>
        </div>

        <div className="flex justify-between mt-10 px-6">
          <div className="text-center">
            <CalendarDays className="mx-auto text-purple-500 mb-1" />
            <p className="text-sm font-semibold text-purple-700">DATE</p>
            <p className="text-sm text-gray-600">{completionDate}</p>
          </div>

          <div className="text-center">
            <PenLine className="mx-auto text-purple-500 mb-1" />
            <p className="text-sm font-semibold text-purple-700">SIGNATURE</p>
            {/* {signature && (
              <img
                src={signature}
                alt="Signature"
                className="h-8 mx-auto mt-1"
              />
            )} */}
            <p className="text-sm text-gray-600">{signedBy}</p>
            <p className="text-xs text-gray-500">{signedByTitle}</p>
          </div>
        </div>
      </div>

      <div className="absolute top-[-40px] left-[-40px] w-40 h-40 bg-gradient-to-br from-orange-400 to-red-500 rounded-full blur-2xl opacity-50 z-0" />
      <div className="absolute bottom-[-40px] right-[-40px] w-40 h-40 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full blur-2xl opacity-50 z-0" />
    </div>
  );
};

export default ModernCertificate;
