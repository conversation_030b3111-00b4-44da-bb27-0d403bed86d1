import React from "react";
import { Trophy } from "lucide-react";
const dummyData = {
  studentName: "Varun",
  programTitle: "MENTAL WELLNESS 101",
  completionDate: "July 15, 2025",
  certificateTitle: "CERTIFICATE OF COMPLETION",
  certificateText:
    "awarded for exceptional participation and excellence in sport",
  issuedBy: "<PERSON><PERSON>",
  issuedByTitle: "Sports Coach",
  signedBy: "<PERSON><PERSON>",
  signedByTitle: "School Head",
  logo: "/logo.png",
  signature: "/signature.png",
};

const Certificate = () => {
  const {
    studentName,
    programTitle,
    completionDate,
    certificateTitle,
    certificateText,
    issuedBy,
    issuedByTitle,
    signedBy,
    signedByTitle,
    logo,
    signature,
  } = dummyData;

  return (
    <div className="w-[1000px] h-auto border-[12px] border-blue-800 rounded-xl bg-white mx-auto p-10 relative font-sans shadow-xl mt-10 mb-20">
      <div className="border-4 border-gray-200 p-8">
        <div className="text-center">
          {logo && <img src={logo} alt="Logo" className="h-14 mx-auto mb-2" />}
          <h1 className="text-2xl font-bold text-blue-800 uppercase tracking-wide">
            {certificateTitle}
          </h1>
          <p className="mt-2 text-gray-700">
            This certificate is proudly presented to
          </p>
          <h2 className="text-4xl font-semibold mt-4 italic text-gray-900">
            {studentName}
          </h2>
          <p className="mt-2 text-gray-700 text-sm">
            has successfully completed the program:
          </p>
          <h3 className="text-xl font-medium text-gray-800 mt-1">
            {programTitle}
          </h3>
          <p className="mt-1 text-gray-700 text-sm">on {completionDate}</p>
          {/* <p className="mt-4 text-gray-600 text-base italic max-w-xl mx-auto">
            {certificateText}
          </p> */}
        </div>

        <div className="flex justify-between items-end mt-12">
          <div className="text-center">
            {signature && (
              <img
                src={signature}
                alt="Signature"
                className="h-10 mx-auto mb-1"
              />
            )}
            <p className="font-semibold text-gray-800">{issuedBy}</p>
            <p className="text-xs text-gray-500">{issuedByTitle}</p>
          </div>

          <div className="flex justify-center mt-10">
            <Trophy
              className="w-20 h-20 text-yellow-500 drop-shadow-md"
              fill="#facc15"
            />
          </div>

          <div className="text-center">
            {signature && (
              <img
                src={signature}
                alt="Signature"
                className="h-10 mx-auto mb-1"
              />
            )}
            <p className="font-semibold text-gray-800">{signedBy}</p>
            <p className="text-xs text-gray-500">{signedByTitle}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Certificate;
