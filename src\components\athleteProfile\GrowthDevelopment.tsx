import { AppDispatch, RootState } from "@/store"
import { fetchAthleteGrowthInterests, handleUpdateUserInput, putAthleteGrowthInterests } from "@/store/slices/athlete/athleteProfileSlice"
import { fetchAthletesInterests } from "@/store/slices/commonSlice"
import { Loader } from "lucide-react"
import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import MultiSelectWithChip from "../common/MultiSelectWithChip"
import { Button } from "../ui/button"
import { Label } from "../ui/label"
import { Textarea } from "../ui/textarea"

const GrowthDevelopment = () => {
    const { selectedGrowthList, achieveOrAccomplish, apiStatus } = useSelector((state: RootState) => state.athleteProfile)
    const { athleteInterestsList } = useSelector((state: RootState) => state.commonSlice)
    const dispatch = useDispatch<AppDispatch>()

    const initialFetch = async () => {
        await dispatch(fetchAthletesInterests())
        await dispatch(fetchAthleteGrowthInterests())
    }

    useEffect(() => {
        initialFetch()
    }, [dispatch])

    const handleOnChange = (name, value) => {
        dispatch(handleUpdateUserInput({ name, value }))
    }

    const handleCancel = () => {
        dispatch(handleUpdateUserInput({ name: 'selectedGrowthList', value: [] }))
        dispatch(handleUpdateUserInput({ name: 'achieveOrAccomplish', value: '' }))
    }

    const handleSave = async () => {
        const payload = {
            interestIds: selectedGrowthList?.map(each => each?.value),
            achievements: achieveOrAccomplish
        }

        try {
            const resultAction = await dispatch(putAthleteGrowthInterests(payload))
            if (putAthleteGrowthInterests.fulfilled.match(resultAction)) {
                await dispatch(fetchAthleteGrowthInterests())
            }
        } catch (error) {
            console.error(error)
        }
    }

    return (
        <>
            <div className="bg-slate-100 rounded-lg p-5 flex flex-col items-center gap-8 px-3 w-full">
                <h5 className="font-bold text-lg text-center">Growth & Development Interests</h5>

                <div className="flex flex-col md:flex-row items-center justify-center gap-5 w-full">
                    <MultiSelectWithChip
                        options={athleteInterestsList}
                        className="w-full md:w-3/4"
                        name="selectedGrowthList"
                        onChange={(list) => handleOnChange('selectedGrowthList', list)}
                        placeholder="Select Interests..."
                        value={selectedGrowthList}
                    />
                </div>

                <div className="flex flex-col justify-center w-full md:w-3/4">
                    <Label className="font-bold my-1">What else would you like to achieve or accomplish?</Label>
                    <Textarea
                        className="border-slate-300 w-full  bg-white "
                        placeholder="Write here..."
                        rows={3}
                        maxLength={200}
                        value={achieveOrAccomplish}
                        onChange={(e) => handleOnChange('achieveOrAccomplish', e.target.value)}
                    />
                    <span className="text-destructive text-xs self-end font-semibold">200 Char Max</span>
                </div>

                <div className="w-full flex justify-end gap-5">
                    <Button onClick={handleSave} className="w-24">
                        {apiStatus === 'putGrowthPending' ?
                            <Loader className='h-4 w-4 animate-spin' />
                            : 'Save'}
                    </Button>
                </div>
            </div>
        </>
    )
}
export default GrowthDevelopment