"use client";

import { resetPaymentStates } from "@/store/slices/premiumSlice";
import { usePathname } from "next/navigation";
import { useEffect } from "react";
import { useDispatch } from "react-redux";

export default function StateResetWatcher() {
    const pathname = usePathname();
    const dispatch = useDispatch();

    useEffect(() => {
        if (pathname === undefined) return;
        const token = localStorage.getItem('token')
        const inPremiumPlan = pathname.includes("premium-plan");

        if (!inPremiumPlan || !token) {
            dispatch(resetPaymentStates());
        }
    }, [pathname, dispatch]);

    return null;
}
